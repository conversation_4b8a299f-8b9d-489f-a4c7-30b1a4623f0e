import { useEffect, useRef } from 'react';

const useDisableMouseWheel = () => {
  const inputRef = useRef<HTMLInputElement | null>(null);

  useEffect(() => {
    const input = inputRef.current;

    if (input) {
      const handleMouseWheel = (event: Event) => {
        (event.target as HTMLElement).blur();
      };

      input.addEventListener("mousewheel", handleMouseWheel);

      return () => {
        input.removeEventListener("mousewheel", handleMouseWheel);
      };
    }
  }, []);

  return inputRef;
};

export default useDisableMouseWheel;