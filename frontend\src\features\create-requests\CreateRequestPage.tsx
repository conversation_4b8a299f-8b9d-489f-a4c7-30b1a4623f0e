import { useCallback, useRef, useState } from "react";
import { Header, MainContent } from "./components";

type Props = {
  handleSidebarOpen: (state: boolean) => void;
  sidebarOpen: boolean;
};

export const CreateRequestPage = ({
  handleSidebarOpen,
  sidebarOpen,
}: Props) => {
  const formRef = useRef<HTMLFormElement>(null);
  const [inputText, setInputText] = useState("");

  const handleReset = useCallback(() => {
    if (formRef.current) {
      formRef.current.reset();
      setInputText("");
    }
  }, []);

  const [loading, setLoading] = useState(false);

  const handleSave = useCallback(async () => {
    if (formRef.current) {
      setLoading(true);
      try {
        await formRef.current.submitForm();
      } finally {
        setLoading(false)
      }
    }
  }, []);

  const handleSidebarIconClick = useCallback(() => {
    handleSidebarOpen(true);
  }, [handleSidebarOpen]);

  return (
    <>
      <Header
        handleSidebarIconClick={handleSidebarIconClick}
        sidebarOpen={sidebarOpen}
        handleReset={handleReset}
        handleSave={handleSave}
        saveRequestLoading={loading}
      />
      <MainContent
        sidebarOpen={sidebarOpen}
        formRef={formRef}
        initalInputText={inputText}
        setInitalInputText={setInputText}
      />
    </>
  );
};
