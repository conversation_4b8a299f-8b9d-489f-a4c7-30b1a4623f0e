/* eslint-disable @typescript-eslint/no-explicit-any */

type MaybePromise<T> = T | Promise<T>;

export function handleError(
    error: unknown,
    handlers: readonly [(abstract new (...args: any[]) => Error) | null, (error: Error) => void][],
    catchAll?: (error: unknown) => void,
) {
    for (const [errorType, handler] of handlers) {
        if (errorType && error instanceof errorType) {
            handler(error);

            return;
        }
    }

    if (catchAll) {
        catchAll(error);

        return;
    }

    throw error;
}

export async function handleErrorAsync(
    error: unknown,
    handlers: readonly [abstract new (...args: any[]) => Error, (error: Error) => MaybePromise<void>][],
    catchAll?: (error: unknown) => MaybePromise<void>,
) {
    for (const [errorType, handler] of handlers) {
        if (error instanceof errorType) {
            await handler(error);

            return;
        }
    }

    if (catchAll) {
        await catchAll(error);

        return;
    }

    throw error;
}

export function trycatch<T>(
    fn: () => T,
    errorHandlers: readonly [abstract new (...args: any[]) => Error, (error: Error) => void][],
    catchAll?: (error: unknown) => void,
) {
    try {
        return fn();
    }
    catch (error) {
        return handleError(error, errorHandlers, catchAll);
    }
}

export async function trycatchAsync<T>(
    fn: () => Promise<T>,
    errorHandlers: readonly [abstract new (...args: any[]) => Error, (error: Error) => MaybePromise<void>][],
    catchAll?: (error: unknown) => MaybePromise<void>,
) {
    try {
        return await fn();
    }
    catch (error) {
        return handleErrorAsync(error, errorHandlers, catchAll);
    }
}
