import type { LlmService } from "../llm";

import { Injectable } from "../utils";
import * as DtoV1 from "./dto-v1";
import * as DtoV2 from "./dto-v2";
import * as Prompt from "./prompts";

export class ExtractService extends Injectable {
    private readonly llmService!: LlmService;

    async extractV1(data: {
        text: string;
    }) {
        return await this.llmService.chatCompletion({
            messages: [
                {
                    role: "system",
                    content: Prompt.v1,
                },
                {
                    role: "user",
                    content: data.text,
                },
            ],
            responseFormat: {
                schema: DtoV1.Extract,
                name: "extract",
            },
        });
    }

    async extractV2(data: {
        text: string;
    }) {
        const [
            metadata,
            digits,
            services,
        ] = await Promise.all([
            this.llmService.chatCompletion({
                messages: [
                    {
                        role: "system",
                        content: Prompt.v2.metadata,
                    },
                    {
                        role: "user",
                        content: data.text,
                    },
                ],
                responseFormat: {
                    schema: DtoV2.ExtractMetadata,
                    name: "metadata",
                },
            }),

            this.llmService.chatCompletion({
                messages: [
                    {
                        role: "system",
                        content: Prompt.v2.digits,
                    },
                    {
                        role: "user",
                        content: data.text,
                    },
                ],
                responseFormat: {
                    schema: DtoV2.ExtractDigits,
                    name: "digits",
                },
            }),

            this.llmService.chatCompletion({
                messages: [
                    {
                        role: "system",
                        content: Prompt.v2.services,
                    },
                    {
                        role: "user",
                        content: data.text,
                    },
                ],
                responseFormat: {
                    schema: DtoV2.ExtractServices,
                    name: "services",
                },
            }),
        ]);

        return {
            metadata,
            digits,
            services,
        };
    }
}
