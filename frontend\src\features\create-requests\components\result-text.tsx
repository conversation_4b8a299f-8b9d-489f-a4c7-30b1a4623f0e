import styles from "./result-text.module.css";

import { CopyButton } from "./copy-button";

type Props = {
  title: string;
  mainContent: string;
};

export const ResultText = ({ title = "", mainContent = "" }: Props) => {
  return (
    <div className={styles.resultTextContainer}>
      <div className={styles.titleResult}>
        <div style={{ flexShrink: 2, overflow: "auto" }}>{title}</div>
        <CopyButton text={title} disabled={!title} />
      </div>

      <div className={styles.fullTextResultContainer}>
        <div className={styles.fullTextResult}>{mainContent}</div>
        <div className={styles.actionBtns}>
          <CopyButton text={mainContent} disabled={!mainContent} />
        </div>
      </div>
    </div>
  );
};
