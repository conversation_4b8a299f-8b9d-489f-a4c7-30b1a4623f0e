import { memo, ReactNode } from "react";
import { Container, FormControl, SxProps, Theme } from "@mui/material";
import { TextFieldWrapper } from "./text-field-wrapper";
import { CustomizedInputLabel } from "./customized-input-label";

type Props = {
  label: string;
  locked?: boolean;
  formName: string | string[];
  maxWidth?: number;
  multiline?: boolean;
  additionalStyling?: SxProps<Theme>;
  handleOnBlur?: () => void;
  handleOnChange?: (value: number) => void;
  error?: string;
  errorWrapper?: ReactNode;
  type?: "number" | "string";
};

export const TextFieldWithLabel = memo(
  ({
    label,
    formName,
    locked = false,
    maxWidth,
    multiline,
    additionalStyling,
    handleOnBlur,
    handleOnChange,
    error,
    errorWrapper,
    type,
  }: Props) => {
    return (
      <FormControl>
        <CustomizedInputLabel
          shrink
          htmlFor="text"
          sx={{ left: "-13px" }}
          fontWeight={500}
        >
          {label}
        </CustomizedInputLabel>
        {Array.isArray(formName) ? (
          <Container
            sx={{
              display: "flex",
              gap: "8px",
              padding: 0,
              flexShrink: 1,
              maxWidth: maxWidth,
            }}
            maxWidth={false}
            disableGutters
          >
            {formName.map((name) => (
              <TextFieldWrapper
                onChange={handleOnChange}
                type={type}
                name={name}
                locked={locked}
                key={`${label}=${name}`}
              />
            ))}
          </Container>
        ) : (
          <TextFieldWrapper
            multiline={multiline}
            name={formName}
            onBlur={handleOnBlur}
            type={type}
            error={error}
            locked={locked}
            onChange={handleOnChange}
            helperWrapper={errorWrapper}
            additionalStyles={{
              flexShrink: 1,
              maxWidth: maxWidth,
              ...additionalStyling,
            }}
          />
        )}
      </FormControl>
    );
  }
);
