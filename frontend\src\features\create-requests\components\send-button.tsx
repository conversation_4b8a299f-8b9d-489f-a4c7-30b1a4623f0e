import { CircularProgress } from "@mui/material";
import { Icon } from "../assets";
import styles from "./send-button.module.css";

type Props = {
  handleSendButtonClick: () => void;
  loading: boolean;
  disabled?: boolean;
};

export const SendButton = ({
  handleSendButtonClick,
  loading,
  disabled,
}: Props) => {
  return (
    <button
      onClick={handleSendButtonClick}
      disabled={loading || disabled}
      className={styles.sendButton}
    >
      {loading ? (
        <CircularProgress
          size={16}
          style={{ position: "absolute", color: "white" }}
        />
      ) : (
        <Icon.Send />
      )}
    </button>
  );
};
