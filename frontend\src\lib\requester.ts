export type SuccessResult<T = unknown> = { success: true, status: number, headers: Headers, body: T };
export type ErrorResult = { success: false, status: number, headers: Headers, body: string | null, error?: never };
export type UnexpectedErrorResult = { success: false, status?: never, headers?: never, error: Error }

export type Result<T = unknown> = SuccessResult<T> | ErrorResult | UnexpectedErrorResult;

export class Requester {
  static get(url: string): Promise<Result>;
  static get<T>(url: string): Promise<Result<T>>;
  static async get(url: string) {
    try {
      const response = await fetch(`/api${url}`, {
        method: "GET",
      });

      if (response.ok) {
        return {
          success: true,
          status: response.status,
          headers: response.headers,
          body: await response.json(),
        } satisfies SuccessResult;
      }

      return {
        success: false,
        status: response.status,
        headers: response.headers,
        body: await response.text().catch(() => null),
      } satisfies ErrorResult;
    }
    catch (e) {
      return {
        success: false,
        error: e instanceof Error ? e : new Error(String(e))
      } satisfies UnexpectedErrorResult;
    }
  }

  static post(url: string, body: object): Promise<Result>;
  static post<T>(url: string, body: object): Promise<Result<T>>;
  static async post(url: string, body: object) {
    try {
      const response = await fetch(`/api${url}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });

      if (response.ok) {
        return {
          success: true,
          status: response.status,
          headers: response.headers,
          body: await response.json().catch(() => undefined),
        } satisfies SuccessResult;
      }

      return {
        success: false,
        status: response.status,
        headers: response.headers,
        body: await response.text().catch(() => null),
      } satisfies ErrorResult;
    }
    catch (e) {
      return {
        success: false,
        error: e instanceof Error ? e : new Error(String(e))
      } satisfies UnexpectedErrorResult;
    }
  }
}
