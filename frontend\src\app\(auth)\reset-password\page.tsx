import {
  Box,
  Button,
  IconButton,
  InputAdornment,
  Stack,
  <PERSON>Field,
  Typography,
} from "@mui/material";
import React, { useState, FormEvent } from "react";
import MailOutlineIcon from "@mui/icons-material/MailOutline";
import VisibilityOutlinedIcon from "@mui/icons-material/VisibilityOutlined";
import VisibilityOffOutlinedIcon from "@mui/icons-material/VisibilityOffOutlined";
import { Requester } from "../../../lib/requester";
import { Modal } from "../../../components";

const OTP_LENGTH = 6;

export function ResetPasswordPage() {
  const [modalError, setModalError] = useState<string | null>(null);
  const [passwordError, setPasswordError] = useState<string | null>(null);

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showOtpScreen, setShowOtpScreen] = useState(false);

  // OTP related state
  const [otpValues, setOtpValues] = useState<string[]>(Array(OTP_LENGTH).fill(''));
  // Create refs for OTP input fields
  const otpRef1 = useState<HTMLInputElement | null>(null);
  const otpRef2 = useState<HTMLInputElement | null>(null);
  const otpRef3 = useState<HTMLInputElement | null>(null);
  const otpRef4 = useState<HTMLInputElement | null>(null);
  const otpRef5 = useState<HTMLInputElement | null>(null);
  const otpRef6 = useState<HTMLInputElement | null>(null);
  // Array of refs for easier access
  const otpRefs = [otpRef1, otpRef2, otpRef3, otpRef4, otpRef5, otpRef6];

  const toggleShowPassword = () => {
    setShowPassword(v => !v);
  };

  const toggleShowConfirmPassword = () => {
    setShowConfirmPassword(v => !v);
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    // Here you would typically validate the form
    // For example, check if passwords match
    if (password !== confirmPassword) {
      setPasswordError("Passwords do not match");

      return;
    }

    if (password.length < 8) {
      setPasswordError("Password must be at least 8 characters long");

      return;
    }

    // Send OTP request
    const result = await Requester.post(
      "/auth/otp",
      {
        email,
      }
    );

    if (result.success) {
      // Reset OTP values when showing OTP screen
      setOtpValues(Array(6).fill(''));
      setShowOtpScreen(true);
    }
    else if (result.error) {
      setModalError(result.error.message);
    }
    else {
      setModalError(`${result.status}: ${result.body}`);
    }
  };

  // Handle OTP input change
  const handleOtpChange = (index: number, value: string) => {
    // Only allow numeric input
    if (value && !/^\d+$/.test(value)) {
      return;
    }

    // Update the OTP values
    const newOtpValues = [...otpValues];
    newOtpValues[index] = value;
    setOtpValues(newOtpValues);

    // If a digit is entered and there's a next input, focus on it
    if (value && index < 5) {
      otpRefs[index + 1][0]?.focus();
    }
  };

  // Handle key down events (for backspace)
  const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    // If backspace is pressed on an empty input and there's a previous input, focus on it
    if (e.key === 'Backspace' && !otpValues[index] && index > 0) {
      otpRefs[index - 1][0]?.focus();
    }
  };

  // Handle paste functionality
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text/plain').trim();

    // If the pasted data is a number
    if (/^\d+$/.test(pastedData)) {
      const newOtpValues = [...otpValues];

      // Fill in the OTP fields with the pasted digits
      for (let i = 0; i < Math.min(pastedData.length, 6); i++) {
        newOtpValues[i] = pastedData[i];
      }

      setOtpValues(newOtpValues);

      // Focus on the next empty field or the last field
      const nextEmptyIndex = newOtpValues.findIndex(val => !val);
      if (nextEmptyIndex !== -1 && nextEmptyIndex < 6) {
        otpRefs[nextEmptyIndex][0]?.focus();
      } else {
        otpRefs[4][0]?.focus();
      }
    }
  };

  const handleOtpSubmit = async (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.preventDefault();

    const otp = otpValues.join('');

    if (otp.length !== OTP_LENGTH) return;

    const result = await Requester.post("/auth/reset-password", {
      email,
      password,
      otp,
    });

    if (result.success) {
      // Redirect to the home page
      location.assign("/");
    }
    else if (result.error) {
      setModalError(result.error.message);
    }
    else {
      switch (result.status) {
        case 400:
          setModalError("Invalid OTP");
          break;

        default:
          setModalError(`${result.status}: ${result.body}`);
      }
    }
  }

  // OTP verification screen
  if (showOtpScreen) {
    return (
      <>
        <title>Reset password - Logispot</title>
        <Modal
          open={modalError !== null}
          onClose={() => setModalError(null)}
          message={modalError}
        />
        <Box sx={{ width: "100%", maxWidth: 620 }}>
          {/* Page Title */}
          <Typography
            variant="h3"
            component="h1"
            sx={{
              fontWeight: 500,
              fontSize: { xs: "2.5rem", sm: "3.125rem", md: "3.75rem" },
              lineHeight: 1.2,
              mb: 2.5,
              fontFamily: "'DM Sans', sans-serif",
            }}
          >
            Last step
          </Typography>

          {/* Page Description */}
          <Typography
            variant="body1"
            sx={{
              fontWeight: 300,
              mb: 3,
              fontSize: "20px",
              letterSpacing: "0.625px",
              fontFamily: "'DM Sans', sans-serif",
            }}
          >
            Please enter the code that we sent you on your email:
          </Typography>

          {/* OTP Code Input */}
          <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
            {Array(OTP_LENGTH).fill(0).map((_, index) => (
              <TextField
                key={index}
                variant="outlined"
                value={otpValues[index]}
                onChange={(e) => handleOtpChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e as React.KeyboardEvent<HTMLInputElement>)}
                onPaste={index === 0 ? handlePaste : undefined}
                inputRef={(el) => otpRefs[index][1](el)}
                autoFocus={index === 0}
                sx={{
                  width: "85px",
                  "& .MuiOutlinedInput-root": {
                    borderRadius: 0,
                    height: "47px",
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#C6C5CA",
                  },
                  "& .MuiInputBase-input": {
                    textAlign: "center",
                    padding: "0",
                  },
                }}
                slotProps={{
                  htmlInput: {
                    maxLength: 1,
                    inputMode: 'numeric',
                    pattern: '[0-9]*'
                  }
                }}
              />
            ))}
          </Stack>

          {/* Send Button */}
          <Button
            fullWidth
            variant="contained"
            onClick={handleOtpSubmit}
            sx={{
              bgcolor: "#70B57D",
              color: "white",
              borderRadius: 0,
              height: "50px",
              textTransform: "none",
              "&:hover": {
                bgcolor: "#5da069",
              },
            }}
          >
            Send
          </Button>
        </Box>
      </>
    );
  }

  // Registration form screen
  return (
    <>
      <title>Reset password - Logispot</title>
      <Box sx={{ width: "100%", maxWidth: 620 }}>
        {/* Page Title */}
        <Typography
          variant="h3"
          component="h1"
          sx={{
            fontWeight: 500,
            fontSize: { xs: "2.5rem", sm: "3.125rem", md: "3.75rem" },
            lineHeight: 1.2,
            mb: 2.5,
            fontFamily: "'DM Sans', sans-serif",
          }}
        >
          Get Early Access – Free Beta Testing
        </Typography>

        {/* Page Description */}
        <Typography
          variant="body1"
          sx={{
            fontWeight: 300,
            mb: 6.25,
            fontSize: "20px",
            letterSpacing: "0.625px",
            fontFamily: "'DM Sans', sans-serif",
          }}
        >
          Sign up now to be among the first to test Logispot for free during our development phase.
        </Typography>

        {/* Form */}
        <form onSubmit={handleSubmit}>
          <Stack spacing={6}>
            {/* Email Input */}
            <TextField
              fullWidth
              label="Email"
              placeholder="Enter your email address"
              variant="outlined"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              type="email"
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <MailOutlineIcon />
                    </InputAdornment>
                  ),
                },
                inputLabel: {
                  shrink: true,
                  sx: {
                    fontWeight: 'bold',
                    fontSize: '14px',
                    transform: 'translate(14px, -6px) scale(1)',
                    '&.MuiInputLabel-shrink': {
                      transform: 'translate(14px, -6px) scale(1)',
                    }
                  }
                }
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  borderRadius: 0,
                  height: "70px",
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#C6C5CA",
                },
                "& .MuiOutlinedInput-notchedOutline legend": {
                  width: 'auto !important',
                  padding: '0 5px',
                  visibility: 'visible',
                },
                "& .MuiInputLabel-root": {
                  color: '#000',
                },
                "& .MuiInputBase-input::placeholder": {
                  paddingLeft: "6px",
                  fontSize: "18px",
                  color: "#C6C5CA",
                  opacity: 1,
                }
              }}
            />

            {/* Password Input */}
            <Box sx={{ position: 'relative' }}>
              <TextField
                fullWidth
                label="Password"
                placeholder="Enter your password"
                type={showPassword ? "text" : "password"}
                autoComplete="off"
                variant="outlined"
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  if (passwordError) setPasswordError(null);
                }}
                required
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position="start">
                        <img
                          src="/images/key.svg"
                          alt="key"
                          width={24}
                          height={24}
                        />
                      </InputAdornment>
                    ),
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={toggleShowPassword}
                          edge="end"
                        >
                          {showPassword ? (
                            <VisibilityOutlinedIcon />
                          ) : (
                            <VisibilityOffOutlinedIcon />
                          )}
                        </IconButton>
                      </InputAdornment>
                    ),
                  },
                  inputLabel: {
                    shrink: true,
                    sx: {
                      fontWeight: 'bold',
                      fontSize: '14px',
                      transform: 'translate(14px, -6px) scale(1)',
                      '&.MuiInputLabel-shrink': {
                        transform: 'translate(14px, -6px) scale(1)',
                      }
                    }
                  }
                }}
                sx={{
                  "& .MuiOutlinedInput-root": {
                    borderRadius: 0,
                    height: "70px",
                  },
                  "& .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#C6C5CA",
                  },
                  "& .MuiOutlinedInput-notchedOutline legend": {
                    width: 'auto !important',
                    padding: '0 5px',
                    visibility: 'visible',
                  },
                  "& .MuiInputLabel-root": {
                    color: '#000',
                  },
                  "& .MuiInputBase-input::placeholder": {
                    paddingLeft: "6px",
                    fontSize: "18px",
                    color: "#C6C5CA",
                    opacity: 1,
                  }
                }}
              />

              {/* Password Error Message */}
              {passwordError && (
                <Typography
                  variant="body2"
                  sx={{
                    color: '#d32f2f',
                    fontSize: '14px',
                    fontFamily: "'DM Sans', sans-serif",
                    position: 'absolute',
                    top: '100%',
                    left: 0,
                    mt: 1.5,
                    zIndex: 1,
                  }}
                >
                  {passwordError}
                </Typography>
              )}
            </Box>

            {/* Confirm Password Input */}
            <TextField
              fullWidth
              label="Confirm Password"
              placeholder="Confirm your password"
              type={showConfirmPassword ? "text" : "password"}
              autoComplete="off"
              variant="outlined"
              value={confirmPassword}
              onChange={(e) => {
                setConfirmPassword(e.target.value);
                if (passwordError) setPasswordError(null);
              }}
              required
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <img
                        src="/images/key.svg"
                        alt="key"
                        width={24}
                        height={24}
                      />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={toggleShowConfirmPassword}
                        edge="end"
                      >
                        {showConfirmPassword ? (
                          <VisibilityOutlinedIcon />
                        ) : (
                          <VisibilityOffOutlinedIcon />
                        )}
                      </IconButton>
                    </InputAdornment>
                  ),
                },
                inputLabel: {
                  shrink: true,
                  sx: {
                    fontWeight: 'bold',
                    fontSize: '14px',
                    transform: 'translate(14px, -6px) scale(1)',
                    '&.MuiInputLabel-shrink': {
                      transform: 'translate(14px, -6px) scale(1)',
                    }
                  }
                }
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  borderRadius: 0,
                  height: "70px",
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "#C6C5CA",
                },
                "& .MuiOutlinedInput-notchedOutline legend": {
                  width: 'auto !important',
                  padding: '0 5px',
                  visibility: 'visible',
                },
                "& .MuiInputLabel-root": {
                  color: '#000',
                },
                "& .MuiInputBase-input::placeholder": {
                  paddingLeft: "6px",
                  fontSize: "18px",
                  color: "#C6C5CA",
                  opacity: 1,
                }
              }}
            />

            {/* Create Account Button */}
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{
                bgcolor: "#70B57D",
                color: "white",
                borderRadius: 0,
                height: "50px",
                textTransform: "none",
                "&:hover": {
                  bgcolor: "#5da069",
                },
              }}
            >
              Reset password
            </Button>
          </Stack>
        </form>
      </Box>
    </>
  );
}
