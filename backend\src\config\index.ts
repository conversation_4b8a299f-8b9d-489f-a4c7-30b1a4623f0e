import type { z } from "zod";

import { Injectable } from "../utils";

export class Config extends Injectable {
    get<T = unknown>(key: string, defaultValue?: T): T {
        const value = process.env[key];

        if (value === undefined) {
            if (defaultValue === undefined) {
                throw new Error(`Config value for key ${key} is not set.`);
            }

            return defaultValue;
        }

        return value as T;
    }

    getWithValidation<T extends z.ZodSchema>(key: string, schema: T): z.output<T> {
        const value = this.get(key);
        const result = schema.safeParse(value);

        if (!result.success) {
            throw new Error(`Invalid config value for key ${key}: ${result.error.message}`);
        }

        return result.data;
    }
}
