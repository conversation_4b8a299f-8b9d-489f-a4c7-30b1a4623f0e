// Simple test script to verify authentication functionality
const axios = require('axios');

const API_URL = 'http://localhost:3001';
let sessionCookie = null;

// Helper function to make API requests
async function apiRequest(method, endpoint, data = null) {
    try {
        const config = {
            method,
            url: `${API_URL}${endpoint}`,
            headers: {
                'Content-Type': 'application/json',
            },
            withCredentials: true,
        };

        if (sessionCookie) {
            config.headers.Cookie = sessionCookie;
        }

        if (data) {
            config.data = data;
        }

        const response = await axios(config);
        
        // Save session cookie if present
        const cookies = response.headers['set-cookie'];
        if (cookies) {
            sessionCookie = cookies[0];
        }
        
        return response.data;
    } catch (error) {
        if (error.response) {
            console.error(`Error ${error.response.status}: ${JSON.stringify(error.response.data)}`);
        } else {
            console.error('Error:', error.message);
        }
        return null;
    }
}

// Test functions
async function testSignUp() {
    console.log('\n--- Testing Sign Up ---');
    const userData = {
        email: `user${Date.now()}@example.com`, // Use timestamp to ensure unique email
        password: 'password123',
        name: 'Test User',
    };
    
    console.log(`Signing up with email: ${userData.email}`);
    const result = await apiRequest('post', '/auth/sign-up', userData);
    
    if (result) {
        console.log('Sign up successful!');
        console.log('User data:', result.user);
        return userData;
    }
    
    return null;
}

async function testSignIn(credentials) {
    console.log('\n--- Testing Sign In ---');
    console.log(`Signing in with email: ${credentials.email}`);
    
    const result = await apiRequest('post', '/auth/sign-in', {
        email: credentials.email,
        password: credentials.password,
    });
    
    if (result) {
        console.log('Sign in successful!');
        console.log('User data:', result.user);
        return true;
    }
    
    return false;
}

async function testGetCurrentUser() {
    console.log('\n--- Testing Get Current User ---');
    const result = await apiRequest('get', '/auth/me');
    
    if (result) {
        console.log('Get current user successful!');
        console.log('User data:', result.user);
        return true;
    }
    
    return false;
}

async function testExtractEndpoint() {
    console.log('\n--- Testing Protected Extract Endpoint ---');
    const result = await apiRequest('post', '/extract', {
        text: 'Sample text for extraction',
    });
    
    if (result) {
        console.log('Extract endpoint access successful!');
        console.log('Extract result:', result);
        return true;
    }
    
    return false;
}

async function testSignOut() {
    console.log('\n--- Testing Sign Out ---');
    const result = await apiRequest('post', '/auth/sign-out');
    
    if (result) {
        console.log('Sign out successful!');
        console.log('Message:', result.message);
        return true;
    }
    
    return false;
}

async function testAfterSignOut() {
    console.log('\n--- Testing Access After Sign Out ---');
    const result = await apiRequest('get', '/auth/me');
    
    if (!result) {
        console.log('Access denied after sign out, as expected!');
        return true;
    }
    
    console.log('WARNING: Still able to access protected route after sign out!');
    return false;
}

// Run all tests
async function runTests() {
    try {
        // Test sign up
        const userData = await testSignUp();
        if (!userData) {
            console.error('Sign up failed, stopping tests.');
            return;
        }
        
        // Test sign in
        const signInSuccess = await testSignIn(userData);
        if (!signInSuccess) {
            console.error('Sign in failed, stopping tests.');
            return;
        }
        
        // Test get current user
        await testGetCurrentUser();
        
        // Test protected endpoint
        await testExtractEndpoint();
        
        // Test sign out
        const signOutSuccess = await testSignOut();
        if (!signOutSuccess) {
            console.error('Sign out failed, stopping tests.');
            return;
        }
        
        // Test access after sign out
        await testAfterSignOut();
        
        console.log('\n--- All tests completed! ---');
    } catch (error) {
        console.error('Test error:', error);
    }
}

// Run the tests
runTests();
