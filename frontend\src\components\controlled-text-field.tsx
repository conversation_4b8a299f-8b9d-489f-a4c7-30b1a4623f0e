import React from "react";
import {
  Controller,
  ControllerFieldState,
  ControllerRenderProps,
  FieldValues,
  useFormContext,
} from "react-hook-form";
import { TextFieldProps } from "@mui/material";
import { CustomizedTextField } from "./customized-text-field";
import { roundUpToTwoDecimals } from "../utils/roundUpToTwoDecimals";

type ControlledTextFieldProps = Omit<TextFieldProps, "name"> & {
  name: string;
  type?: "number" | "string";
  onBaseChange?: (value: number) => void;
};

const blockInvalidChar = (e: React.KeyboardEvent<HTMLDivElement>) =>
  ["e", "E", "+", "-"].includes(e.key) && e.preventDefault();

const handleNumberInputChange = (
  e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
) => {
  const value = e.target.value;

  if (value === "") {
    return null;
  }
  return parseFloat(value);
};

export const ControlledTextField = ({
  name,
  type = "string",
  onBaseChange,
  helperText,
  ...textFieldProps
}: ControlledTextFieldProps) => {
  const { clearErrors } = useFormContext();

  const renderTextField = (
    field: ControllerRenderProps<FieldValues, string>,
    fieldState: ControllerFieldState
  ) => (
    <CustomizedTextField
      {...field}
      {...textFieldProps}
      onBlur={(e) => {
        field.onBlur();
        textFieldProps.onBlur?.(e);
      }}
      value={type === 'number' ? roundUpToTwoDecimals(field.value) : field.value}
      onChange={(e) => {
        clearErrors(name);
        const result =
          type === "number" ? handleNumberInputChange(e) : e.target.value;
        field.onChange(result);
        if (type === "number") {
          onBaseChange?.(Number(result) || 0);
        }
        textFieldProps?.onChange?.(e);
      }}
      onKeyDown={type === "number" ? blockInvalidChar : undefined}
      slotProps={{
        ...(type === "number"
          ? { htmlInput: { type: "number", min: "0" } }
          : {}),
        ...textFieldProps.slotProps,
      }}
      error={!!fieldState.error}
      helperText={!helperText ? fieldState.error?.message : helperText}
    />
  );

  return (
    <Controller
      render={({ field, fieldState }) => renderTextField(field, fieldState)}
      name={name}
    />
  );
};
