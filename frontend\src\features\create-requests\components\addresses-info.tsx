import { SelectOption } from "../utils/useGetSelectOptions";
import styles from "./addresses-info.module.css";

import {
  AddressInfo,
  DeliveryInfo,
} from ".";

type Props = {
  deliveryOptions: SelectOption;
};

export const AddressesInfo = ({ deliveryOptions }: Props) => {
  return (
    <div className={styles.addressesInfoContainer}>
      <DeliveryInfo  {...deliveryOptions} />
      <AddressInfo
        label="Pick-up address"
        checkboxLabel="yes, we need pick up"
        type="pickup"
      />

      <AddressInfo
        label="Delivery address"
        checkboxLabel="yes, we need address delivery"
        type="delivery"
      />
    </div>
  );
};
