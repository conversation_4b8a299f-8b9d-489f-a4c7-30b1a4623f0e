import { useState } from 'react';
import {
  Box,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  Chip,
  IconButton,
  InputAdornment,
  styled
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import AddIcon from '@mui/icons-material/Add';
import './figma.css';

const StyledButton = styled(Button)({
  color: "white",
  fontSize: "12px",
  lineHeight: "16px",
  textTransform: "none",
  fontWeight: "400",
  borderRadius: "0",
  height: "40px",
});

const FilterButton = styled(Button)({
  fontSize: "12px",
  lineHeight: "16px",
  textTransform: "none",
  fontWeight: "700",
  height: "40px",
  padding: "8px 12px",
});

// Mock data for the table
const mockRequests = [
  {
    id: 1,
    date: '01.04.2025',
    requestId: 'LG6750',
    origin: 'Paris',
    destination: 'Hamburg Billwerder',
    quantity: 4,
    weight: 0.506,
    volume: 246.10,
    chargeableWeight: 246.10,
    status: 'pending'
  },
  {
    id: 2,
    date: '01.04.2025',
    requestId: 'LG6751',
    origin: 'Paris',
    destination: 'Frankfurt am Main',
    quantity: 4,
    weight: 0.506,
    volume: 246.10,
    chargeableWeight: 246.10,
    status: 'pending'
  },
  {
    id: 3,
    date: '01.04.2025',
    requestId: 'LG6752',
    origin: 'Paris',
    destination: 'London',
    quantity: 4,
    weight: 0.506,
    volume: 246.10,
    chargeableWeight: 246.10,
    status: 'rejected'
  },
  {
    id: 4,
    date: '01.04.2025',
    requestId: 'LG6753',
    origin: 'Paris',
    destination: 'London',
    quantity: 4,
    weight: 0.506,
    volume: 246.10,
    chargeableWeight: 246.10,
    status: 'order'
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return { backgroundColor: 'rgba(179, 179, 179, 0.2)', color: '#707070' };
    case 'order':
      return { backgroundColor: 'rgba(112, 181, 125, 0.2)', color: '#70B57D' };
    case 'calculating':
      return { backgroundColor: 'rgba(255, 165, 0, 0.2)', color: '#FFA500' };
    case 'rejected':
      return { backgroundColor: 'rgba(254, 140, 140, 0.44)', color: '#E84B4B' };
    default:
      return { backgroundColor: 'rgba(179, 179, 179, 0.2)', color: '#707070' };
  }
};

export function RequestsPage() {
  const [searchValue, setSearchValue] = useState('');
  const [origin, setOrigin] = useState('Paris');
  const [destination, setDestination] = useState('Paris');
  const [service, setService] = useState('Air freight');
  const [dateRange, setDateRange] = useState('01.04-05.05');

  return (
    <Box sx={{
      padding: '35px 16px',
      backgroundColor: '#FFFFFF',
      minHeight: '100vh'
    }}>
      {/* Header Section */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '27px'
      }}>
        {/* Search Input - Left Side */}
        <TextField
          placeholder="Search"
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          sx={{
            width: '729px',
            height: '40px',
            '& .MuiOutlinedInput-root': {
              backgroundColor: '#F7F7F7',
              height: '40px',
              fontSize: '16px',
              fontWeight: 300,
              color: 'rgba(44, 42, 41, 0.5)',
              border: 'none',
              '& fieldset': {
                border: 'none',
              },
            }
          }}
          slotProps={{
            input: {
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: '#1A1A1A' }} />
                </InputAdornment>
              ),
            }
          }}
        />

        {/* Action Buttons - Right Side */}
        <Box sx={{ display: 'flex', gap: '16px' }}>
          <StyledButton
            sx={{
              backgroundColor: "rgba(112, 181, 125, 1)",
              width: "186px",
            }}
            startIcon={<AddIcon />}
          >
            Create new request
          </StyledButton>
          <StyledButton
            sx={{
              border: "1px solid rgba(26, 26, 26, 1)",
              width: "92px",
              color: "black",
              backgroundColor: "transparent"
            }}
          >
            Sign in
          </StyledButton>
        </Box>
      </Box>

      {/* Filters Section */}
      <Box sx={{
        display: 'flex',
        gap: '20px',
        marginBottom: '27px',
        alignItems: 'flex-end'
      }}>
        {/* Origin Filter */}
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: '5px', width: '205px' }}>
          <Box sx={{
            fontSize: '12px',
            fontWeight: 400,
            color: 'rgba(44, 42, 41, 0.5)',
            fontFamily: "'YS Text', sans-serif"
          }}>
            Origin
          </Box>
          <FormControl fullWidth>
            <Select
              value={origin}
              onChange={(e) => setOrigin(e.target.value)}
              sx={{
                height: '40px',
                backgroundColor: '#F7F7F7',
                fontSize: '16px',
                fontWeight: 300,
                color: 'rgba(44, 42, 41, 0.5)',
                '& .MuiOutlinedInput-notchedOutline': {
                  border: 'none',
                },
                '& .MuiSelect-icon': {
                  transform: 'rotate(-90deg)',
                  color: '#1A1A1A'
                }
              }}
            >
              <MenuItem value="Paris">Paris</MenuItem>
              <MenuItem value="London">London</MenuItem>
              <MenuItem value="Berlin">Berlin</MenuItem>
            </Select>
          </FormControl>
        </Box>

        {/* Destination Filter */}
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: '5px', width: '205px' }}>
          <Box sx={{
            fontSize: '12px',
            fontWeight: 400,
            color: 'rgba(44, 42, 41, 0.5)',
            fontFamily: "'YS Text', sans-serif"
          }}>
            Destination
          </Box>
          <FormControl fullWidth>
            <Select
              value={destination}
              onChange={(e) => setDestination(e.target.value)}
              sx={{
                height: '40px',
                backgroundColor: '#F7F7F7',
                fontSize: '16px',
                fontWeight: 300,
                color: 'rgba(44, 42, 41, 0.5)',
                '& .MuiOutlinedInput-notchedOutline': {
                  border: 'none',
                },
                '& .MuiSelect-icon': {
                  transform: 'rotate(-90deg)',
                  color: '#1A1A1A'
                }
              }}
            >
              <MenuItem value="Paris">Paris</MenuItem>
              <MenuItem value="Hamburg Billwerder">Hamburg Billwerder</MenuItem>
              <MenuItem value="Frankfurt am Main">Frankfurt am Main</MenuItem>
              <MenuItem value="London">London</MenuItem>
            </Select>
          </FormControl>
        </Box>

        {/* Service Filter */}
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: '5px', width: '205px' }}>
          <Box sx={{
            fontSize: '12px',
            fontWeight: 400,
            color: 'rgba(44, 42, 41, 0.5)',
            fontFamily: "'YS Text', sans-serif"
          }}>
            Service
          </Box>
          <FormControl fullWidth>
            <Select
              value={service}
              onChange={(e) => setService(e.target.value)}
              sx={{
                height: '40px',
                backgroundColor: '#F7F7F7',
                fontSize: '16px',
                fontWeight: 300,
                color: 'rgba(44, 42, 41, 0.5)',
                '& .MuiOutlinedInput-notchedOutline': {
                  border: 'none',
                },
                '& .MuiSelect-icon': {
                  transform: 'rotate(-90deg)',
                  color: '#1A1A1A'
                }
              }}
            >
              <MenuItem value="Air freight">Air freight</MenuItem>
              <MenuItem value="Sea freight">Sea freight</MenuItem>
              <MenuItem value="Road freight">Road freight</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Box>

      {/* Date Period Section */}
      <Box sx={{
        display: 'flex',
        gap: '13px',
        marginBottom: '40px',
        alignItems: 'center'
      }}>
        {/* Date Range Button */}
        <FilterButton
          sx={{
            backgroundColor: '#70B57D',
            color: '#EFEFEF',
            width: '128px',
            gap: '10px'
          }}
          startIcon={<CalendarTodayIcon sx={{ color: '#EFEFEF' }} />}
        >
          {dateRange}
        </FilterButton>

        {/* Quick Date Buttons */}
        <FilterButton
          sx={{
            backgroundColor: '#EFEFEF',
            color: '#1A1A1A',
            width: '61px'
          }}
          onClick={() => setDateRange('Today')}
        >
          Today
        </FilterButton>

        <FilterButton
          sx={{
            backgroundColor: '#EFEFEF',
            color: '#1A1A1A',
            width: '90px'
          }}
          onClick={() => setDateRange('This week')}
        >
          This week
        </FilterButton>

        <FilterButton
          sx={{
            backgroundColor: '#EFEFEF',
            color: '#1A1A1A',
            width: '97px'
          }}
          onClick={() => setDateRange('This month')}
        >
          This month
        </FilterButton>
      </Box>

      {/* Table Section */}
      <Box sx={{
        backgroundColor: '#FFFFFF',
        borderRadius: '8px 8px 0px 0px',
        overflow: 'hidden'
      }}>
        {/* Table Header */}
        <Box sx={{
          backgroundColor: '#70B57D',
          height: '66px',
          display: 'flex',
          alignItems: 'center',
          padding: '0 12px',
          borderRadius: '8px 8px 0px 0px'
        }}>
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%',
            gap: '12px'
          }}>
            {/* Column Headers */}
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              width: '884px'
            }}>
              <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
                Request date
              </Box>
              <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
                Request id
              </Box>
              <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
                Origin
              </Box>
              <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
                Destination
              </Box>
              <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
                Quantity
              </Box>
              <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
                Weight
              </Box>
              <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
                Volume
              </Box>
              <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
                Chargeable weight
              </Box>
            </Box>

            {/* Actions Header */}
            <Box sx={{ width: '217px', fontSize: '12px', fontWeight: 700, color: '#FFFFFF', fontFamily: "'YS Text', sans-serif" }}>
              Status & Actions
            </Box>
          </Box>
        </Box>

        {/* Table Body */}
        <Box>
          {mockRequests.map((request, index) => (
            <Box key={request.id}>
              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '0 12px',
                height: '54px',
                gap: '12px'
              }}>
                {/* Data Columns */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  width: '884px'
                }}>
                  <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 400, color: '#1A1A1A', fontFamily: "'YS Text', sans-serif" }}>
                    {request.date}
                  </Box>
                  <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 400, color: '#1A1A1A', fontFamily: "'YS Text', sans-serif" }}>
                    {request.requestId}
                  </Box>
                  <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 400, color: '#1A1A1A', fontFamily: "'YS Text', sans-serif" }}>
                    {request.origin}
                  </Box>
                  <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 400, color: '#1A1A1A', fontFamily: "'YS Text', sans-serif", lineHeight: '16px' }}>
                    {request.destination}
                  </Box>
                  <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 400, color: '#1A1A1A', fontFamily: "'YS Text', sans-serif" }}>
                    {request.quantity}
                  </Box>
                  <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 400, color: '#1A1A1A', fontFamily: "'YS Text', sans-serif" }}>
                    {request.weight}
                  </Box>
                  <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 400, color: '#1A1A1A', fontFamily: "'YS Text', sans-serif" }}>
                    {request.volume}
                  </Box>
                  <Box sx={{ width: '100px', fontSize: '12px', fontWeight: 400, color: '#1A1A1A', fontFamily: "'YS Text', sans-serif" }}>
                    {request.chargeableWeight}
                  </Box>
                </Box>

                {/* Actions Column */}
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '22px',
                  width: '217px'
                }}>
                  {/* Status Chip */}
                  <Chip
                    label={request.status}
                    sx={{
                      ...getStatusColor(request.status),
                      fontSize: '12px',
                      fontWeight: 700,
                      fontFamily: "'YS Text', sans-serif",
                      height: '16px',
                      width: '79px',
                      '& .MuiChip-label': {
                        padding: 0
                      }
                    }}
                  />

                  {/* Action Buttons */}
                  <IconButton size="small" sx={{ color: '#1B1D1F' }}>
                    <DeleteIcon />
                  </IconButton>
                  <IconButton size="small" sx={{ color: '#1B1D1F' }}>
                    <EditIcon />
                  </IconButton>
                  <IconButton size="small" sx={{ color: '#C6C5CA', transform: 'rotate(90deg)' }}>
                    <ExpandMoreIcon />
                  </IconButton>
                </Box>
              </Box>

              {/* Divider */}
              {index < mockRequests.length - 1 && (
                <Box sx={{
                  height: '1px',
                  backgroundColor: '#EFEFEF',
                  width: '100%'
                }} />
              )}
            </Box>
          ))}
        </Box>
      </Box>
    </Box>
  );
}