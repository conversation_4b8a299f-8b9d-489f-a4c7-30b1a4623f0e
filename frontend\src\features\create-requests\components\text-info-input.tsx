import { Button, <PERSON>con<PERSON>utton } from "@mui/material";
import { useRef, useState } from "react";
import { CustomAlert } from "../../../components";
import { Icon } from "../assets";
import styles from "./text-info-input.module.css";

type Props = {
  text: string;
  handleTextChange: (newText: string) => void;
};

export const TextInfoInput = ({ text, handleTextChange }: Props) => {
  const pasteAreaRef = useRef<HTMLTextAreaElement | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleTextClear = () => {
    handleTextChange("");
  };

  const handlePaste = async () => {
    setError(null);
    try {
      const clipboardText = await navigator.clipboard.readText();
      const newText =
        text.trim() === "" ? clipboardText : text + "\n" + clipboardText;

      handleTextChange(newText);
    } catch {
      setError("Failed to read clipboard contents");
    }
  };
  return (
    <>
      <CustomAlert message={error} severity="error" />
      <div className={styles.textIntoInputContainer}>
        <div className={styles.textFieldContainer}>
          <textarea
            className={styles.textField}
            ref={pasteAreaRef}
            value={text}
            onChange={(e) => handleTextChange(e.target.value)}
          ></textarea>
          <IconButton
            size="small"
            sx={{
              position: "absolute",
              margin: "2px 2px",
              right: 0,
              top: 0,
            }}
            onClick={handleTextClear}
          >
            <Icon.Close />
          </IconButton>
        </div>

        <Button
          disableElevation
          onClick={handlePaste}
          startIcon={<Icon.Paste />}
          size="small"
          variant="text"
          sx={{
            fontSize: "12px",
            textTransform: "lowercase",
            cursor: "pointer",
            fontWeight: "400",
            lineHeight: '16px',
            marginTop: "5px",
          }}
        >
          paste
        </Button>
      </div>
    </>
  );
};
