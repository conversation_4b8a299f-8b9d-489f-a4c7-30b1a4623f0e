import { useFormContext } from "react-hook-form";
import { Base, Detail, SummaryFieldKey } from "../types/shippingTypes";
import { getTotalFieldsSum } from "./calculateTotalPackageDetails";

const useSubscribeToPackageUpdates = () => {
  const { getValues, setValue, trigger } = useFormContext();

  const updateValue = ({
    fieldKey,
    base,
    details,
  }: {
    fieldKey: SummaryFieldKey;
    base: Base;
    details: Detail[];
  }) => {
    const result = getTotalFieldsSum(fieldKey, details);
    const totalSum = base[fieldKey] + result;

    if (totalSum < 0) {
      setValue(`summary.${fieldKey}`, 0);
    } else {
      setValue(`summary.${fieldKey}`, totalSum);
    }
    trigger(`summary.${fieldKey}`);
  };

  const handleRemove = (base: Base) => {
    const details = getValues("details");
    const fieldKeys: SummaryFieldKey[] = ["piece", "volume", "weight"];
    fieldKeys.forEach((fieldKey) => {
      updateValue({
        fieldKey: fieldKey,
        base: base,
        details: details,
      });
    });
  };

  const handlePackageAddition = () => {
    const currentValue = getValues("summary.piece");
    setValue("summary.piece", currentValue + 1);
    trigger(`summary.piece`);
  };

  return { updateValue, handleRemove, handlePackageAddition };
};

export default useSubscribeToPackageUpdates;
