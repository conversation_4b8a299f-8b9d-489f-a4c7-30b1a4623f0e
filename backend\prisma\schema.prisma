// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  @@map("users")

  id String @id @default(nanoid())

  email String @unique
  hash  String

  country String @default("France")

  createdAt DateTime @map("created_at") @default(now())
  updatedAt DateTime @map("updated_at") @updatedAt

  @@index([email])
}

model EmailOtp {
  @@map("email_otps")

  id String @id @default(nanoid())

  email String
  otp   String

  expiresAt DateTime @map("expires_at")

  createdAt DateTime @map("created_at") @default(now())
  updatedAt DateTime @map("updated_at") @updatedAt

  @@index([email, otp])
  @@unique([email, otp])
}
