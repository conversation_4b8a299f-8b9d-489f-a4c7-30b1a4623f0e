import { z } from "zod";
import { getTotalFieldsSum } from "./calculateTotalPackageDetails";
import {
  VALUE_NOT_PROVIDED,
  VALUE_SHOULD_BE_BIGGER_THAN_0,
  ERROR,
  REQUIRED_WHEN_INCOTERMS_IS_EXW,
  REQUIRED_FOR_DAP_OR_DDP_INCOTERMS,
  REQUIRED,
  NO_MATCH_FROM_PACKAGES,
  NO_MATCH_DIMENSIONS,
} from "./errorMessages";
import {
  getVolumeFromDimensions,
  isCityValid,
  isTotalEqualToDetailed,
  validateIncotermsAddress,
  validateIncotermsPickupCity,
  validateVolume,
} from "./validationFunctions";
import { DELIVERY_REQUIRED_INCOTERMS } from "./consts";
import { roundUpToTwoDecimals } from "../../../utils/roundUpToTwoDecimals";

export const customErrorMap: z.ZodErrorMap = (error) => {
  switch (error.code) {
    case z.ZodIssueCode.invalid_type: {
      if (error.received === "null") {
        return { message: VALUE_NOT_PROVIDED };
      }
      break;
    }

    case z.ZodIssueCode.too_small: {
      if (error.type === "number") {
        return { message: VALUE_SHOULD_BE_BIGGER_THAN_0 };
      }
    }
  }

  return { message: ERROR };
};

z.setErrorMap(customErrorMap);

export const ShippingInfoSchemaBase = z.object({
  service_type: z.string().optional(),
  incoterms: z.string().optional(),
  origin: z.string(),
  destination_country: z.string(),
  pickup: z
    .object({
      is_needed: z.boolean().optional(),
      city: z.string().optional(),
      zip_code: z.string().optional(),
      address: z.string().optional(),
    })
    .optional(),
  delivery: z
    .object({
      is_needed: z.boolean().optional(),
      city: z.string().optional(),
      zip_code: z.string().optional(),
      address: z.string().optional(),
    })
    .optional(),
  summary: z.object({
    piece: z.number().positive(),
    weight: z.number().positive(),
    volume: z.number().positive(),
    density: z.number(),
    chargeable_weight: z.number().optional(),
  }),
  details: z.array(
    z.object({
      piece: z.number().positive(),
      package_type: z.string().optional(),
      dimension: z
        .object({
          width: z.number().nullish(),
          height: z.number().nullish(),
          length: z.number().nullish(),
          volume: z.number().nullish(),
          weight: z.number().optional(),
          is_stackable: z.boolean().optional(),
        })
        .optional()
        .superRefine((data, ctx) => {
          const isValid = validateVolume(data);

          if (!isValid) {
            ctx.addIssue({
              message: `${NO_MATCH_DIMENSIONS}: ${getVolumeFromDimensions(
                data,
                true
              )}m3`,
              path: ["volume"],
              code: z.ZodIssueCode.custom,
            });
          }
        }),
    })
  ),
  additional_details: z
    .object({
      costs_of_goods: z.number().nullish(),
      description_of_goods: z.string().optional(),
      dangerous_goods: z.array(z.string().optional()).optional(),
      selected_services: z.array(z.string().optional()).optional(),
      services: z.string().optional(),
      hs_codes: z.string().or(z.number()).optional(),
    })
    .optional(),
});

export const ShippingSchemaWithValidation = ShippingInfoSchemaBase.refine(
  (data) => validateIncotermsPickupCity(data),
  {
    path: ["pickup", "city"],
    message: REQUIRED_WHEN_INCOTERMS_IS_EXW,
  }
)
  .refine((data) => validateIncotermsAddress(data), {
    path: ["pickup", "address"],
    message: REQUIRED_WHEN_INCOTERMS_IS_EXW,
  })
  .refine((data) => isCityValid(data, "pickup"), {
    path: ["pickup", "city"],
    message: REQUIRED,
  })
  .refine(
    ({ incoterms, delivery }) =>
      !DELIVERY_REQUIRED_INCOTERMS.includes(incoterms ?? "") || delivery?.city,
    {
      path: ["delivery", "city"],
      message: REQUIRED_FOR_DAP_OR_DDP_INCOTERMS,
    }
  )
  .refine(
    ({ incoterms, delivery }) =>
      !DELIVERY_REQUIRED_INCOTERMS.includes(incoterms || "") ||
      delivery?.address,
    {
      path: ["delivery", "address"],
      message: REQUIRED_FOR_DAP_OR_DDP_INCOTERMS,
    }
  )
  .refine((data) => isCityValid(data, "delivery"), {
    path: ["delivery", "city"],
    message: REQUIRED,
  })
  .refine((data) => !!data.origin, {
    path: ["origin"],
    message: REQUIRED,
  })
  .refine((data) => !!data.destination_country, {
    path: ["destination_country"],
    message: REQUIRED,
  })
  .superRefine((data, ctx) => {
    const totalDetailsWeight = getTotalFieldsSum("weight", data.details);
    const isValid = isTotalEqualToDetailed({
      total: data.summary.weight,
      detailed: totalDetailsWeight,
    });
    if (isValid) {
      return;
    }
    ctx.addIssue({
      message: `${NO_MATCH_FROM_PACKAGES}: ${roundUpToTwoDecimals(totalDetailsWeight)}kg`,
      path: ["summary", "weight"],
      code: z.ZodIssueCode.custom,
    });
  })
  .superRefine((data, ctx) => {
    const totalDetailsPiece = getTotalFieldsSum("piece", data.details);
    const isValid = isTotalEqualToDetailed({
      total: data.summary.piece,
      detailed: totalDetailsPiece,
    });

    if (isValid) {
      return;
    }

    ctx.addIssue({
      message: `${NO_MATCH_FROM_PACKAGES}: ${totalDetailsPiece}`,
      path: ["summary", "piece"],
      code: z.ZodIssueCode.custom,
    });
  })
  .superRefine((data, ctx) => {
    const totalDetailsVolume = getTotalFieldsSum("volume", data.details);

    const isValid = isTotalEqualToDetailed({
      total: data.summary.volume,
      detailed: totalDetailsVolume,
    });

    if (!isValid) {
      ctx.addIssue({
        message: `${NO_MATCH_FROM_PACKAGES}: ${roundUpToTwoDecimals(totalDetailsVolume)}`,
        path: ["summary", "volume"],
        code: z.ZodIssueCode.custom,
      });
    }
  });
