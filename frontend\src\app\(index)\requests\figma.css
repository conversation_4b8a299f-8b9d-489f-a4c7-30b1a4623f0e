/* Screen 1- my requests */

position: relative;
width: 1440px;
height: 875px;

background: #FFFFFF;
box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);


/* Rectangle 43 */

position: absolute;
width: 1208px;
height: 66px;
left: 216px;
top: 256px;

background: #70B57D;
border-radius: 8px 8px 0px 0px;


/* Rectangle 5 */

position: absolute;
width: 191px;
height: 1003px;
left: 0px;
top: 0px;

background: #70B57D;


/* Label(Replace) */

position: absolute;
visibility: hidden;
width: 425px;
height: 86px;
left: 854px;
top: 157px;

font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;



/* Logo */

position: absolute;
width: 119px;
height: 28.12px;
left: calc(50% - 119px/2 - 637.5px);
top: 39px;



/* logo_text */

position: absolute;
width: 81.66px;
height: 12.74px;
left: 60.34px;
top: 46.6px;



/* Vector */

position: absolute;
left: 6.63%;
right: 93.18%;
top: 5.33%;
bottom: 93.24%;

background: #FFFFFF;


/* Vector */

position: absolute;
left: 4.74%;
right: 94.38%;
top: 5.33%;
bottom: 93.22%;

background: #FFFFFF;


/* Vector */

position: absolute;
left: 5.67%;
right: 93.46%;
top: 5.33%;
bottom: 93.22%;

background: #FFFFFF;


/* Vector */

position: absolute;
left: 6.89%;
right: 92.46%;
top: 5.33%;
bottom: 93.22%;

background: #FFFFFF;


/* Vector */

position: absolute;
left: 8.31%;
right: 90.8%;
top: 5.33%;
bottom: 93.22%;

background: #FFFFFF;


/* Vector */

position: absolute;
left: 4.19%;
right: 95.25%;
top: 5.35%;
bottom: 93.24%;

background: #FFFFFF;


/* Vector */

position: absolute;
left: 7.63%;
right: 91.71%;
top: 5.35%;
bottom: 93.24%;

background: #FFFFFF;


/* Vector */

position: absolute;
left: 9.18%;
right: 90.14%;
top: 5.35%;
bottom: 93.24%;

background: #FFFFFF;


/* logo_circle */

position: absolute;
width: 28.12px;
height: 28.12px;
left: 23px;
top: 39px;



/* Ellipse 1 */

position: absolute;
width: 28.12px;
height: 28.12px;
left: 23px;
top: 39px;

background: #FFFFFF;


/* letters */

position: absolute;
width: 11.76px;
height: 14.1px;
left: 31.15px;
top: 45.79px;



/* Vector */

position: absolute;
left: 2.58%;
right: 97.02%;
top: 5.23%;
bottom: 94%;

background: #1A1A1A;


/* Vector */

position: absolute;
left: 2.2%;
right: 97.47%;
top: 5.25%;
bottom: 94.02%;

background: #1A1A1A;


/* Vector */

position: absolute;
left: 2.16%;
right: 97.45%;
top: 6.08%;
bottom: 93.16%;

background: #1A1A1A;


/* Vector */

position: absolute;
left: 2.62%;
right: 97.06%;
top: 6.09%;
bottom: 93.17%;

background: #1A1A1A;


/* Menu */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 6px;

position: absolute;
width: 189px;
height: 194px;
left: 0px;
top: 126px;



/* menu-item_act_home */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 10px 10px 10px 20px;
gap: 10px;

width: 189px;
height: 44px;

border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* file */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Path  */

position: absolute;
left: 54.17%;
right: 20.83%;
top: 12.5%;
bottom: 62.5%;

border: 1px solid #FFFFFF;
border-radius: 0.5px;


/* Rectangle  */

position: absolute;
left: 79.17%;
right: -54.17%;
top: 12.5%;
bottom: 29.17%;

border: 1px solid #FFFFFF;
border-radius: 0.5px;
transform: rotate(90deg);


/* text */

width: 152px;
height: 16px;

font-family: 'YS Text';
font-style: normal;
font-weight: 300;
font-size: 16px;
line-height: 16px;
/* identical to box height, or 100% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* menu-item_def_requests */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 10px 10px 10px 20px;
gap: 10px;

width: 185px;
height: 44px;

background: #568D60;
border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* folder */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 12.5%;
right: 12.5%;
top: 16.67%;
bottom: 16.67%;

border: 1px solid #FFFFFF;
border-radius: 0.5px;


/* text */

width: 141px;
height: 16px;

/* ys_big_light */
font-family: 'YS Text';
font-style: normal;
font-weight: 300;
font-size: 16px;
line-height: 16px;
/* identical to box height, or 100% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* menu-item_def_dashboard */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 10px 10px 10px 20px;
gap: 10px;

width: 185px;
height: 44px;

border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* column-chart */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Path */

position: absolute;
left: 12.5%;
right: 70.83%;
top: 54.17%;
bottom: 16.67%;

border: 1px solid #FFFFFF;
border-radius: 0.5px;


/* Path */

position: absolute;
left: 41.67%;
right: 41.67%;
top: 16.67%;
bottom: 16.67%;

border: 1px solid #FFFFFF;
border-radius: 0.5px;


/* Path */

position: absolute;
left: 70.83%;
right: 12.5%;
top: 33.33%;
bottom: 16.67%;

border: 1px solid #FFFFFF;
border-radius: 0.5px;


/* text */

width: 141px;
height: 16px;

/* ys_big_light */
font-family: 'YS Text';
font-style: normal;
font-weight: 300;
font-size: 16px;
line-height: 16px;
/* identical to box height, or 100% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* menu-item_def_orders */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 10px 10px 10px 20px;
gap: 10px;

width: 185px;
height: 44px;

border-radius: 2px;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* settings */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 12.35%;
right: 12.35%;
top: 9.54%;
bottom: 9.54%;

border: 1px solid #FFFFFF;


/* text */

width: 141px;
height: 16px;

/* ys_big_light */
font-family: 'YS Text';
font-style: normal;
font-weight: 300;
font-size: 16px;
line-height: 16px;
/* identical to box height, or 100% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* align-right */

position: absolute;
width: 30px;
height: 30px;
left: calc(50% - 30px/2 - 550px);
top: calc(50% - 30px/2 - 383.5px);



/* Path  */

position: absolute;
left: 79.17%;
right: -12.5%;
top: 45.83%;
bottom: 45.83%;

border: 1px solid #FFFFFF;
transform: matrix(-1, 0, 0, 1, 0, 0);


/* Path  */

position: absolute;
left: 79.17%;
right: -37.5%;
top: 25%;
bottom: 66.67%;

border: 1px solid #FFFFFF;
transform: matrix(-1, 0, 0, 1, 0, 0);


/* Path  */

position: absolute;
left: 79.17%;
right: -29.17%;
top: 66.67%;
bottom: 25%;

border: 1px solid #FFFFFF;
transform: matrix(-1, 0, 0, 1, 0, 0);


/* Submit */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 16px;
gap: 4px;
isolation: isolate;

position: absolute;
width: 186px;
height: 40px;
left: 1188px;
top: 35px;

background: #70B57D;


/* person

account, face, human, people, person, profile, user
*/

display: none;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
z-index: 0;


/* plus */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;
z-index: 1;


/* Vector */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 16.67%;
bottom: 16.67%;

border: 1px solid #FFFFFF;


/* Text */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 6px;
gap: 10px;

width: 126px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;
z-index: 2;


/* Create new request */

width: 114px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Badge */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px 6px;
gap: 10px;

display: none;
width: 47px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;
z-index: 3;


/* 99+ */

width: 34px;
height: 24px;

font-family: 'DM Sans';
font-style: normal;
font-weight: 400;
font-size: 18px;
line-height: 24px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;

/* Community/White */
color: #FFFFFF;

opacity: 0.6;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* notification */

position: absolute;
display: none;
width: 12px;
height: 12px;
right: -4px;
top: -4px;

background: #FF0000;

/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;
z-index: 4;


/* Help_center */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 10px 10px 10px 28px;
gap: 5px;

position: absolute;
width: 159px;
height: 52px;
left: 20px;
top: 800px;

border: 1px solid #E6EFFE;


/* chat */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Shape */

position: absolute;
left: 12.5%;
right: 12.5%;
top: 12.5%;
bottom: 12.5%;

border: 1px solid #FFFFFF;


/* Help us improve – leave a review. */

width: 110px;
height: 32px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 117 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 10px;
gap: 1px;

position: absolute;
width: 43px;
height: 43px;
left: 1381px;
top: 32px;

background: #70B57D;
border-radius: 100px;


/* NM */

width: 27px;
height: 22px;

font-family: 'SF Pro Text';
font-style: normal;
font-weight: 700;
font-size: 16px;
line-height: 22px;
/* identical to box height, or 138% */
text-align: center;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 580678431 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 27px;

position: absolute;
width: 729px;
height: 195px;
left: 216px;
top: 35px;



/* Поиск */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 4px 0px 4px 16px;
gap: 597px;

width: 729px;
height: 40px;

background: #F7F7F7;

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Frame 580678425 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 4px;

width: 80px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* search */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Vector */

position: absolute;
left: 12.5%;
right: 14.63%;
top: 12.5%;
bottom: 14.63%;

/* black */
background: #1A1A1A;


/* Search */

width: 52px;
height: 16px;

/* ys_big_light */
font-family: 'YS Text';
font-style: normal;
font-weight: 300;
font-size: 16px;
line-height: 16px;
/* identical to box height, or 100% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: rgba(44, 42, 41, 0.5);


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 580678429 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 20px;

width: 655px;
height: 61px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Фильтр */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 205px;
height: 61px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Фильтр с названием */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 5px;

width: 205px;
height: 61px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Origin */

width: 36px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: rgba(44, 42, 41, 0.5);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 580678427 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 8px 16px;
gap: 4px;

width: 205px;
height: 40px;

background: #F7F7F7;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 580678428 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 154px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Paris */

width: 37px;
height: 16px;

/* ys_big_light */
font-family: 'YS Text';
font-style: normal;
font-weight: 300;
font-size: 16px;
line-height: 16px;
/* identical to box height, or 100% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: rgba(44, 42, 41, 0.5);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* back */

width: 20px;
height: 20px;

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Vector */

position: absolute;
left: 0%;
right: 0%;
top: 104.17%;
bottom: -104.17%;

opacity: 0.87;
transform: rotate(-90deg);


/* Vector */

position: absolute;
left: 10.93%;
right: 44.32%;
top: 78.13%;
bottom: -56.32%;

/* black */
background: #1A1A1A;
transform: rotate(-90deg);


/* Фильтр */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 205px;
height: 61px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Фильтр с названием */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 5px;

width: 205px;
height: 61px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Destination */

width: 68px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: rgba(44, 42, 41, 0.5);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 580678427 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 8px 16px;
gap: 4px;

width: 205px;
height: 40px;

background: #F7F7F7;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 580678428 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 154px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Paris */

width: 37px;
height: 16px;

/* ys_big_light */
font-family: 'YS Text';
font-style: normal;
font-weight: 300;
font-size: 16px;
line-height: 16px;
/* identical to box height, or 100% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: rgba(44, 42, 41, 0.5);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* back */

width: 20px;
height: 20px;

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Vector */

position: absolute;
left: 0%;
right: 0%;
top: 104.17%;
bottom: -104.17%;

opacity: 0.87;
transform: rotate(-90deg);


/* Vector */

position: absolute;
left: 10.93%;
right: 44.32%;
top: 78.13%;
bottom: -56.32%;

background: #1A1A1A;
transform: rotate(-90deg);


/* Фильтр */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

width: 205px;
height: 61px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Фильтр с названием */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;
gap: 5px;

width: 205px;
height: 61px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Service */

width: 43px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: rgba(44, 42, 41, 0.5);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Frame 580678427 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 8px 16px;
gap: 4px;

width: 205px;
height: 40px;

background: #F7F7F7;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 580678428 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;

width: 154px;
height: 16px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Air freight */

width: 77px;
height: 16px;

/* ys_big_light */
font-family: 'YS Text';
font-style: normal;
font-weight: 300;
font-size: 16px;
line-height: 16px;
/* identical to box height, or 100% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: rgba(44, 42, 41, 0.5);


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* back */

width: 20px;
height: 20px;

transform: rotate(-90deg);

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Vector */

position: absolute;
left: 0%;
right: 0%;
top: 104.17%;
bottom: -104.17%;

opacity: 0.87;
transform: rotate(-90deg);


/* Vector */

position: absolute;
left: 10.93%;
right: 44.32%;
top: 78.13%;
bottom: -56.32%;

background: #1A1A1A;
transform: rotate(-90deg);


/* Frame 580678430 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 13px;

width: 404px;
height: 40px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Кнопка */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px 12px;
gap: 10px;

width: 128px;
height: 40px;

background: #70B57D;

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* calendar

date, event, appointment, schedule
*/

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Icon */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 12.5%;
bottom: 12.5%;

border: 2px solid #EFEFEF;
border-radius: 2px;


/* 01.04-05.05 */

width: 70px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #EFEFEF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Кнопка */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px 12px;
gap: 10px;

width: 61px;
height: 40px;

background: #EFEFEF;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* calendar

date, event, appointment, schedule
*/

display: none;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Today */

width: 37px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Кнопка */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px 12px;
gap: 10px;

width: 84px;
height: 40px;

background: #EFEFEF;

/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* calendar

date, event, appointment, schedule
*/

display: none;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* This week */

width: 60px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Кнопка */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 8px 12px;
gap: 10px;

width: 92px;
height: 40px;

background: #EFEFEF;

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* calendar

date, event, appointment, schedule
*/

display: none;
width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* This Month */

width: 68px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Название таблицы */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 1px;

position: absolute;
width: 884px;
height: 32px;
left: 228px;
top: 273px;



/* Frame 580678432 */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: flex-start;
padding: 0px;
gap: 12px;

width: 884px;
height: 32px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 1;


/* Request date */

width: 100px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Request id */

width: 100px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Origin */

width: 100px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Destination */

width: 100px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Quantity */

width: 100px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* Weight */

width: 100px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* Volume */

width: 100px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
display: flex;
align-items: center;
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* Chargeable weight */

width: 100px;
height: 32px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFFFFF;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* Frame 580678436 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-start;
padding: 0px;

position: absolute;
width: 1208px;
height: 550px;
left: 216px;
top: 322px;



/* Frame 580678433 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-end;
padding: 0px;

width: 1208px;
height: 55px;


/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* Line-1 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px;
gap: 12px;

width: 1196px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 12px;

margin: 0 auto;
width: 884px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 01.04.2025 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* LG6750 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Paris */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* London */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* 4 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* 0.506 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 22px;

margin: 0 auto;
width: 217px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Order_made_marker */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 1px;

width: 79px;
height: 16px;

background: rgba(179, 179, 179, 0.2);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* pending */

width: 50px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #707070;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* trash-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Vector */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 8.33%;
bottom: 8.33%;

/* Black */
border: 1px solid #1B1D1F;


/* edit-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Rectangle  */

position: absolute;
left: 70.83%;
right: 5.6%;
top: 12.5%;
bottom: -0.89%;

/* Black */
border: 1px solid #1B1D1F;
transform: rotate(45deg);


/* Path */

position: absolute;
left: 58.33%;
right: 25%;
top: 25%;
bottom: 58.33%;

/* Black */
border: 2px solid #1B1D1F;


/* Path  */

position: absolute;
left: 50%;
right: 16.67%;
top: 79.17%;
bottom: 12.5%;

/* Black */
border: 1px solid #1B1D1F;


/* open_tarif */

width: 24px;
height: 24px;

transform: rotate(90deg);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Vector */

position: absolute;
left: 75%;
right: 0%;
top: 37.5%;
bottom: 12.5%;

/* grey */
border: 1px solid #C6C5CA;
transform: rotate(90deg);


/* Rectangle 47 */

width: 1209px;
height: 1px;

background: #EFEFEF;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 580678443 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-end;
padding: 0px;

width: 1208px;
height: 55px;


/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* Line-1 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px;
gap: 12px;

width: 1196px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 12px;

margin: 0 auto;
width: 884px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 01.04.2025 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* LG6750 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Paris */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Hamburg Billwerder */

width: 100px;
height: 32px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* 4 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* 0.506 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 22px;

margin: 0 auto;
width: 217px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Order_made_marker */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 1px;

width: 79px;
height: 16px;

background: rgba(179, 179, 179, 0.2);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* pending */

width: 50px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #707070;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* trash-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Vector */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 8.33%;
bottom: 8.33%;

/* Black */
border: 1px solid #1B1D1F;


/* edit-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Rectangle  */

position: absolute;
left: 70.83%;
right: 5.6%;
top: 12.5%;
bottom: -0.89%;

/* Black */
border: 1px solid #1B1D1F;
transform: rotate(45deg);


/* Path */

position: absolute;
left: 58.33%;
right: 25%;
top: 25%;
bottom: 58.33%;

/* Black */
border: 2px solid #1B1D1F;


/* Path  */

position: absolute;
left: 50%;
right: 16.67%;
top: 79.17%;
bottom: 12.5%;

/* Black */
border: 1px solid #1B1D1F;


/* open_tarif */

width: 24px;
height: 24px;

transform: rotate(90deg);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Vector */

position: absolute;
left: 75%;
right: 0%;
top: 37.5%;
bottom: 12.5%;

/* grey */
border: 1px solid #C6C5CA;
transform: rotate(90deg);


/* Rectangle 47 */

width: 1209px;
height: 1px;

background: #EFEFEF;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 580678444 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-end;
padding: 0px;

width: 1208px;
height: 55px;


/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* Line-1 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px;
gap: 12px;

width: 1196px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 12px;

margin: 0 auto;
width: 884px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 01.04.2025 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* LG6750 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Paris */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Frankfurt am Main */

width: 100px;
height: 32px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* 4 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* 0.506 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 22px;

margin: 0 auto;
width: 217px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Order_made_marker */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 1px;

width: 79px;
height: 16px;

background: rgba(179, 179, 179, 0.2);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* pending */

width: 50px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #707070;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* trash-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Vector */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 8.33%;
bottom: 8.33%;

/* Black */
border: 1px solid #1B1D1F;


/* edit-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Rectangle  */

position: absolute;
left: 70.83%;
right: 5.6%;
top: 12.5%;
bottom: -0.89%;

/* Black */
border: 1px solid #1B1D1F;
transform: rotate(45deg);


/* Path */

position: absolute;
left: 58.33%;
right: 25%;
top: 25%;
bottom: 58.33%;

/* Black */
border: 2px solid #1B1D1F;


/* Path  */

position: absolute;
left: 50%;
right: 16.67%;
top: 79.17%;
bottom: 12.5%;

/* Black */
border: 1px solid #1B1D1F;


/* open_tarif */

width: 24px;
height: 24px;

transform: rotate(90deg);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Vector */

position: absolute;
left: 75%;
right: 0%;
top: 37.5%;
bottom: 12.5%;

/* grey */
border: 1px solid #C6C5CA;
transform: rotate(90deg);


/* Rectangle 47 */

width: 1209px;
height: 1px;

background: #EFEFEF;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 580678445 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-end;
padding: 0px;

width: 1208px;
height: 55px;


/* Inside auto layout */
flex: none;
order: 3;
align-self: stretch;
flex-grow: 0;


/* Line-1 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px;
gap: 12px;

width: 1196px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 12px;

margin: 0 auto;
width: 884px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 01.04.2025 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* LG6750 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Paris */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* London */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* 4 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* 0.506 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 22px;

margin: 0 auto;
width: 217px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Markers */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 1px;

width: 79px;
height: 16px;

background: rgba(254, 140, 140, 0.44);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* rejected */

width: 51px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #E84B4B;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* trash-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Vector */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 8.33%;
bottom: 8.33%;

/* Black */
border: 1px solid #1B1D1F;


/* edit-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Rectangle  */

position: absolute;
left: 70.83%;
right: 5.6%;
top: 12.5%;
bottom: -0.89%;

/* Black */
border: 1px solid #1B1D1F;
transform: rotate(45deg);


/* Path */

position: absolute;
left: 58.33%;
right: 25%;
top: 25%;
bottom: 58.33%;

/* Black */
border: 2px solid #1B1D1F;


/* Path  */

position: absolute;
left: 50%;
right: 16.67%;
top: 79.17%;
bottom: 12.5%;

/* Black */
border: 1px solid #1B1D1F;


/* open_tarif */

width: 24px;
height: 24px;

transform: rotate(90deg);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Vector */

position: absolute;
left: 75%;
right: 0%;
top: 37.5%;
bottom: 12.5%;

/* grey */
border: 1px solid #C6C5CA;
transform: rotate(90deg);


/* Rectangle 47 */

width: 1209px;
height: 1px;

background: #EFEFEF;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 580678446 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-end;
padding: 0px;

width: 1208px;
height: 55px;


/* Inside auto layout */
flex: none;
order: 4;
align-self: stretch;
flex-grow: 0;


/* Line-1 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px;
gap: 12px;

width: 1196px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 12px;

margin: 0 auto;
width: 884px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 01.04.2025 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* LG6750 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Paris */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* London */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* 4 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* 0.506 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 22px;

margin: 0 auto;
width: 217px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Markers */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 1px;

width: 79px;
height: 16px;

background: rgba(255, 185, 0, 0.2);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* calculating */

width: 67px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFB900;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* trash-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Vector */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 8.33%;
bottom: 8.33%;

/* Black */
border: 1px solid #1B1D1F;


/* edit-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Rectangle  */

position: absolute;
left: 70.83%;
right: 5.6%;
top: 12.5%;
bottom: -0.89%;

/* Black */
border: 1px solid #1B1D1F;
transform: rotate(45deg);


/* Path */

position: absolute;
left: 58.33%;
right: 25%;
top: 25%;
bottom: 58.33%;

/* Black */
border: 2px solid #1B1D1F;


/* Path  */

position: absolute;
left: 50%;
right: 16.67%;
top: 79.17%;
bottom: 12.5%;

/* Black */
border: 1px solid #1B1D1F;


/* open_tarif */

width: 24px;
height: 24px;

transform: rotate(90deg);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Vector */

position: absolute;
left: 75%;
right: 0%;
top: 37.5%;
bottom: 12.5%;

/* grey */
border: 1px solid #C6C5CA;
transform: rotate(90deg);


/* Rectangle 47 */

width: 1209px;
height: 1px;

background: #EFEFEF;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 580678447 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-end;
padding: 0px;

width: 1208px;
height: 55px;


/* Inside auto layout */
flex: none;
order: 5;
align-self: stretch;
flex-grow: 0;


/* Line-1 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px;
gap: 12px;

width: 1196px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 12px;

margin: 0 auto;
width: 884px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 01.04.2025 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* LG6750 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Paris */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* London */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* 4 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* 0.506 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 22px;

margin: 0 auto;
width: 217px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Markers */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 1px;

width: 79px;
height: 16px;

background: rgba(112, 181, 125, 0.2);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* order */

width: 33px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* trash-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Vector */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 8.33%;
bottom: 8.33%;

/* Black */
border: 1px solid #1B1D1F;


/* edit-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Rectangle  */

position: absolute;
left: 70.83%;
right: 5.6%;
top: 12.5%;
bottom: -0.89%;

/* Black */
border: 1px solid #1B1D1F;
transform: rotate(45deg);


/* Path */

position: absolute;
left: 58.33%;
right: 25%;
top: 25%;
bottom: 58.33%;

/* Black */
border: 2px solid #1B1D1F;


/* Path  */

position: absolute;
left: 50%;
right: 16.67%;
top: 79.17%;
bottom: 12.5%;

/* Black */
border: 1px solid #1B1D1F;


/* open_tarif */

width: 24px;
height: 24px;

transform: rotate(90deg);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Vector */

position: absolute;
left: 75%;
right: 0%;
top: 37.5%;
bottom: 12.5%;

/* grey */
border: 1px solid #C6C5CA;
transform: rotate(90deg);


/* Rectangle 47 */

width: 1209px;
height: 1px;

background: #EFEFEF;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 580678448 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-end;
padding: 0px;

width: 1208px;
height: 55px;


/* Inside auto layout */
flex: none;
order: 6;
align-self: stretch;
flex-grow: 0;


/* Line-1 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px;
gap: 12px;

width: 1196px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 12px;

margin: 0 auto;
width: 884px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 01.04.2025 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* LG6750 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Paris */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* London */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* 4 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* 0.506 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 22px;

margin: 0 auto;
width: 217px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Markers */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 1px;

width: 79px;
height: 16px;

background: rgba(112, 181, 125, 0.2);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* order */

width: 33px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* trash-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Vector */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 8.33%;
bottom: 8.33%;

/* Black */
border: 1px solid #1B1D1F;


/* edit-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Rectangle  */

position: absolute;
left: 70.83%;
right: 5.6%;
top: 12.5%;
bottom: -0.89%;

/* Black */
border: 1px solid #1B1D1F;
transform: rotate(45deg);


/* Path */

position: absolute;
left: 58.33%;
right: 25%;
top: 25%;
bottom: 58.33%;

/* Black */
border: 2px solid #1B1D1F;


/* Path  */

position: absolute;
left: 50%;
right: 16.67%;
top: 79.17%;
bottom: 12.5%;

/* Black */
border: 1px solid #1B1D1F;


/* open_tarif */

width: 24px;
height: 24px;

transform: rotate(90deg);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Vector */

position: absolute;
left: 75%;
right: 0%;
top: 37.5%;
bottom: 12.5%;

/* grey */
border: 1px solid #C6C5CA;
transform: rotate(90deg);


/* Rectangle 47 */

width: 1209px;
height: 1px;

background: #EFEFEF;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 580678449 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-end;
padding: 0px;

width: 1208px;
height: 55px;


/* Inside auto layout */
flex: none;
order: 7;
align-self: stretch;
flex-grow: 0;


/* Line-1 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px;
gap: 12px;

width: 1196px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 12px;

margin: 0 auto;
width: 884px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 01.04.2025 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* LG6750 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Paris */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* London */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* 4 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* 0.506 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 22px;

margin: 0 auto;
width: 217px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Markers */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 1px;

width: 79px;
height: 16px;

background: rgba(112, 181, 125, 0.2);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* order */

width: 33px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* trash-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Vector */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 8.33%;
bottom: 8.33%;

/* Black */
border: 1px solid #1B1D1F;


/* edit-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Rectangle  */

position: absolute;
left: 70.83%;
right: 5.6%;
top: 12.5%;
bottom: -0.89%;

/* Black */
border: 1px solid #1B1D1F;
transform: rotate(45deg);


/* Path */

position: absolute;
left: 58.33%;
right: 25%;
top: 25%;
bottom: 58.33%;

/* Black */
border: 2px solid #1B1D1F;


/* Path  */

position: absolute;
left: 50%;
right: 16.67%;
top: 79.17%;
bottom: 12.5%;

/* Black */
border: 1px solid #1B1D1F;


/* open_tarif */

width: 24px;
height: 24px;

transform: rotate(90deg);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Vector */

position: absolute;
left: 75%;
right: 0%;
top: 37.5%;
bottom: 12.5%;

/* grey */
border: 1px solid #C6C5CA;
transform: rotate(90deg);


/* Rectangle 47 */

width: 1209px;
height: 1px;

background: #EFEFEF;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 580678450 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-end;
padding: 0px;

width: 1208px;
height: 55px;


/* Inside auto layout */
flex: none;
order: 8;
align-self: stretch;
flex-grow: 0;


/* Line-1 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px;
gap: 12px;

width: 1196px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 12px;

margin: 0 auto;
width: 884px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 01.04.2025 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* LG6750 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Paris */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* London */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* 4 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* 0.506 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 22px;

margin: 0 auto;
width: 217px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Markers */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 1px;

width: 79px;
height: 16px;

background: rgba(112, 181, 125, 0.2);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* order */

width: 33px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* trash-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Vector */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 8.33%;
bottom: 8.33%;

/* Black */
border: 1px solid #1B1D1F;


/* edit-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Rectangle  */

position: absolute;
left: 70.83%;
right: 5.6%;
top: 12.5%;
bottom: -0.89%;

/* Black */
border: 1px solid #1B1D1F;
transform: rotate(45deg);


/* Path */

position: absolute;
left: 58.33%;
right: 25%;
top: 25%;
bottom: 58.33%;

/* Black */
border: 2px solid #1B1D1F;


/* Path  */

position: absolute;
left: 50%;
right: 16.67%;
top: 79.17%;
bottom: 12.5%;

/* Black */
border: 1px solid #1B1D1F;


/* open_tarif */

width: 24px;
height: 24px;

transform: rotate(90deg);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Vector */

position: absolute;
left: 75%;
right: 0%;
top: 37.5%;
bottom: 12.5%;

/* grey */
border: 1px solid #C6C5CA;
transform: rotate(90deg);


/* Rectangle 47 */

width: 1209px;
height: 1px;

background: #EFEFEF;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Frame 580678451 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-end;
padding: 0px;

width: 1208px;
height: 55px;


/* Inside auto layout */
flex: none;
order: 9;
align-self: stretch;
flex-grow: 0;


/* Line-1 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: space-between;
align-items: center;
padding: 0px;
gap: 12px;

width: 1196px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* text */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 12px;

margin: 0 auto;
width: 884px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* 01.04.2025 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* LG6750 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Paris */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* London */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* 4 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 4;
flex-grow: 0;


/* 0.506 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 5;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 6;
flex-grow: 0;


/* 246.10 */

width: 100px;
height: 16px;

/* ys_small_regular */
font-family: 'YS Text';
font-style: normal;
font-weight: 400;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

/* black */
color: #1A1A1A;


/* Inside auto layout */
flex: none;
order: 7;
flex-grow: 0;


/* buttons */

/* Auto layout */
display: flex;
flex-direction: row;
align-items: center;
padding: 0px;
gap: 22px;

margin: 0 auto;
width: 217px;
height: 54px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Markers */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 1px;

width: 79px;
height: 16px;

background: rgba(112, 181, 125, 0.2);

/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* order */

width: 33px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* trash-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Vector */

position: absolute;
left: 16.67%;
right: 16.67%;
top: 8.33%;
bottom: 8.33%;

/* Black */
border: 1px solid #1B1D1F;


/* edit-alt */

width: 24px;
height: 24px;


/* Inside auto layout */
flex: none;
order: 2;
flex-grow: 0;


/* Rectangle  */

position: absolute;
left: 70.83%;
right: 5.6%;
top: 12.5%;
bottom: -0.89%;

/* Black */
border: 1px solid #1B1D1F;
transform: rotate(45deg);


/* Path */

position: absolute;
left: 58.33%;
right: 25%;
top: 25%;
bottom: 58.33%;

/* Black */
border: 2px solid #1B1D1F;


/* Path  */

position: absolute;
left: 50%;
right: 16.67%;
top: 79.17%;
bottom: 12.5%;

/* Black */
border: 1px solid #1B1D1F;


/* open_tarif */

width: 24px;
height: 24px;

transform: rotate(90deg);

/* Inside auto layout */
flex: none;
order: 3;
flex-grow: 0;


/* Vector */

position: absolute;
left: 75%;
right: 0%;
top: 37.5%;
bottom: 12.5%;

/* grey */
border: 1px solid #C6C5CA;
transform: rotate(90deg);


/* Rectangle 47 */

width: 1209px;
height: 1px;

background: #EFEFEF;

/* Inside auto layout */
flex: none;
order: 1;
flex-grow: 0;


/* Choose marker */

position: absolute;
width: 99px;
height: 77px;
left: 1197px;
top: 361px;



/* Rectangle 48 */

box-sizing: border-box;

position: absolute;
width: 99px;
height: 77px;
left: 1197px;
top: 361px;

background: #FFFFFF;
border: 1px solid #F8F8F8;
box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.15);
border-radius: 4px;


/* Frame 580678443 */

/* Auto layout */
display: flex;
flex-direction: column;
align-items: flex-end;
padding: 0px;
gap: 6px;

position: absolute;
width: 81px;
height: 60px;
left: 1206px;
top: 370px;



/* Markers_hover/Variant6 */

box-sizing: border-box;

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 1px;

width: 81px;
height: 16px;

background: rgba(112, 181, 125, 0.2);
border: 1px solid #70B57D;

/* Inside auto layout */
flex: none;
order: 0;
align-self: stretch;
flex-grow: 0;


/* order */

width: 33px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Markers_hover/Variant5 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 1px;

width: 81px;
height: 16px;

background: rgba(255, 185, 0, 0.2);

/* Inside auto layout */
flex: none;
order: 1;
align-self: stretch;
flex-grow: 0;


/* calculating */

width: 67px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #FFB900;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;


/* Markers_hover/Variant4 */

/* Auto layout */
display: flex;
flex-direction: row;
justify-content: center;
align-items: center;
padding: 0px;
gap: 1px;

width: 81px;
height: 16px;

background: rgba(254, 140, 140, 0.44);

/* Inside auto layout */
flex: none;
order: 2;
align-self: stretch;
flex-grow: 0;


/* rejected */

width: 51px;
height: 16px;

/* ys_small_bold */
font-family: 'YS Text';
font-style: normal;
font-weight: 700;
font-size: 12px;
line-height: 16px;
/* identical to box height, or 133% */
letter-spacing: 0.5px;
font-feature-settings: 'liga' off;

color: #E84B4B;


/* Inside auto layout */
flex: none;
order: 0;
flex-grow: 0;
