.textIntoInputContainer {
    position: relative;
    min-width: calc(50% - 8px);
    flex-shrink: 1;
    flex-grow: 1;
}

.textFieldContainer {
    width: 100%;
    height: 177px;
    position: relative;
}

.textField {
    width: 100%;
    height: 177px;
    border-radius: 10px;
    border: 1px solid #C6C5CA;
    padding: 14px 26px 14px 24px;
    resize: none;
    box-sizing: border-box;
    font-size: 12px;
    font-weight: 400;
    line-height: 140%;
}

.textField:focus-visible {
    outline: none;
}
