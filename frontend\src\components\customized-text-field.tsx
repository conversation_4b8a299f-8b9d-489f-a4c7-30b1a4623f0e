import { styled } from "@mui/material";
import TextField from "@mui/material/TextField";

export const CustomizedTextField = styled(TextField)`
  & .MuiOutlinedInput-root {
    border-radius: 0;
    font-size: 12px;
    min-height: 40px;
  }

  & .MuiInput-input {
    font-size: 12px;
  }

  & label.MuiInputLabel-shrink {
    color: black;
    font-weight: 700;
    font-size: 12px;
    margin-top: 4px;
  }

  & .Mui-focused .MuiOutlinedInput-notchedOutline {
    border: 2px solid green !important;
  }

  & .MuiFormHelperText-root {
    margin: 0;
    font-size: 10px;
    margin-top: 4px;
  }
`;
