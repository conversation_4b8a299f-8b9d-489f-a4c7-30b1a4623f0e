import { ShippingInfo } from "../types/shippingTypes";

export const defaultShippingInfo: ShippingInfo = {
  service_type: "",
  incoterms: "",
  origin: "",
  destination_country: "",
  pickup: {
    is_needed: false,
    city: "",
    zip_code: "",
    address: "",
  },
  delivery: {
    is_needed: false,
    city: "",
    zip_code: "",
    address: "",
  },
  summary: {
    piece: 0,
    weight: 0,
    volume: 0,
    density: 0,
    chargeable_weight: 0,
  },
  additional_details: {
    costs_of_goods: 0,
    description_of_goods: "",
    selected_services: [],
    dangerous_goods: [],
    services: "",
    hs_codes: 0,
  },
  details: [],
};

export const SERVER_URL = import.meta.env.VITE_SERVER_URL;

export const DELIVERY_REQUIRED_INCOTERMS = ["DAP", "DPU", "DDP"];
export const PICKUP_REQUIRED_INCOTERMS = ["EXW"];
