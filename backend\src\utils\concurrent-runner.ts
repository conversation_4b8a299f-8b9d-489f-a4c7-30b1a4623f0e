type Task<T> = () => Promise<T>;

export class ConcurrentRunner {
    static run<T>(tasks: Task<T>[], maxConcurrent = 1) {
        let done = 0;

        return new Promise<T[]>((resolve, reject) => {
            const results: T[] = [];

            const queue = [...tasks]
                .map((task, i) => [i, task] as const)
                .reverse();

            function runNextFromQueue() {
                const queueTask = queue.pop();

                if (queueTask) {
                    const [i, task] = queueTask;

                    task()
                        .then(result => {
                            results[i] = result;
                            done++;
                        })
                        .catch(reject)
                        .then(runNextFromQueue);
                }
                else {
                    if (done === tasks.length) {
                        resolve(results);
                    }
                }
            }

            for (let i = 0; i < maxConcurrent; i++) {
                runNextFromQueue();
            }
        });
    }
}
