import { useState } from "react";
import { Sidebar, Main } from "../../components";
import { Outlet } from "react-router";

export function IndexLayout() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  const handleSidebarToggle = () => {
    setIsSidebarOpen(v => !v)
  }

  return (
    <>
      <Sidebar handleSidebarClose={handleSidebarToggle} open={isSidebarOpen} />
      <Main open={isSidebarOpen}>
        <Outlet context={{ handleSidebarToggle, sidebarOpen: isSidebarOpen }} />
      </Main>
    </>
  );
};
