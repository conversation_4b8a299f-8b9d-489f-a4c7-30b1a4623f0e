import { useState } from "react";
import { Sidebar } from "../../components";
import { Outlet } from "react-router";

export function IndexLayout() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  const handleSidebarToggle = () => {
    setIsSidebarOpen(v => !v)
  }

  return (
    <>
      <Sidebar handleSidebarClose={handleSidebarToggle} open={isSidebarOpen} />
      <Outlet />
    </>
  );
};
