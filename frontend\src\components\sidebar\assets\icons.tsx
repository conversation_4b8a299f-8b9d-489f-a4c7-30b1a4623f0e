export const CreateRequest = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13 3V8.5C13 8.77614 13.2239 9 13.5 9H19"
      stroke="white"
      strokeLinejoin="round"
    />
    <path
      d="M18.8536 7.85355L14.1464 3.14645C14.0527 3.05268 13.9255 3 13.7929 3L5.5 3.00002C5.22386 3.00002 5 3.22388 5 3.50002L5 20.5C5 20.7761 5.22386 21 5.5 21L18.5 21C18.7761 21 19 20.7761 19 20.5L19 8.20711C19 8.0745 18.9473 7.94732 18.8536 7.85355Z"
      stroke="white"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Dashboard = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.5 13C3.22386 13 3 13.2239 3 13.5L3 19.5C3 19.7761 3.22386 20 3.5 20H6.5C6.77614 20 7 19.7761 7 19.5V13.5C7 13.2239 6.77614 13 6.5 13H3.5Z"
      stroke="white"
      strokeLinecap="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.5 4C10.2239 4 10 4.22386 10 4.5L10 19.5C10 19.7761 10.2239 20 10.5 20H13.5C13.7761 20 14 19.7761 14 19.5V4.5C14 4.22386 13.7761 4 13.5 4H10.5Z"
      stroke="white"
      strokeLinecap="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.5 8C17.2239 8 17 8.22386 17 8.5V19.5C17 19.7761 17.2239 20 17.5 20H20.5C20.7761 20 21 19.7761 21 19.5V8.5C21 8.22386 20.7761 8 20.5 8H17.5Z"
      stroke="white"
      strokeLinecap="round"
    />
  </svg>
);

export const Folder = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M21 18.2222C21 18.6937 20.8104 19.1459 20.4728 19.4793C20.1352 19.8127 19.6774 20 19.2 20H4.8C4.32261 20 3.86477 19.8127 3.52721 19.4793C3.18964 19.1459 3 18.6937 3 18.2222V5.77778C3 5.30628 3.18964 4.8541 3.52721 4.5207C3.86477 4.1873 4.32261 4 4.8 4H9.03425C9.20041 4 9.35572 4.08254 9.44868 4.22026L10.9513 6.4464C11.0443 6.58412 11.1996 6.66667 11.3657 6.66667H19.2C19.6774 6.66667 20.1352 6.85397 20.4728 7.18737C20.8104 7.52076 21 7.97295 21 8.44444V18.2222Z"
      stroke="white"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Settings = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.2836 2.28955C10.4228 2.28955 9.65849 2.84041 9.38627 3.6571L8.97122 4.90223C8.92706 5.0347 8.82913 5.14189 8.70353 5.20291C8.37632 5.3619 8.06231 5.54382 7.76359 5.74657C7.64787 5.82512 7.50586 5.85648 7.36884 5.82844L6.0816 5.56502C5.23821 5.39244 4.37902 5.77889 3.94859 6.52442L3.23221 7.76522C2.80178 8.51075 2.8967 9.44806 3.46785 10.0922L4.34 11.0757C4.43256 11.1801 4.47644 11.3183 4.46656 11.4575C4.45384 11.6367 4.44737 11.8176 4.44737 12.0001C4.44737 12.1825 4.45384 12.3635 4.46656 12.5427C4.47644 12.6819 4.43256 12.8201 4.34 12.9245L3.46785 13.908C2.8967 14.5521 2.80178 15.4894 3.23221 16.2349L3.94859 17.4758C4.37902 18.2213 5.23821 18.6077 6.0816 18.4351L7.36883 18.1717C7.50585 18.1437 7.64786 18.1751 7.76358 18.2536C8.0623 18.4564 8.37631 18.6383 8.70353 18.7973C8.82913 18.8583 8.92706 18.9655 8.97122 19.098L9.38627 20.3431C9.65849 21.1598 10.4228 21.7106 11.2836 21.7106H12.7164C13.5773 21.7106 14.3415 21.1598 14.6138 20.3431L15.0288 19.098C15.073 18.9655 15.1709 18.8583 15.2965 18.7973C15.6237 18.6383 15.9377 18.4564 16.2364 18.2536C16.3521 18.1751 16.4941 18.1437 16.6312 18.1718L17.9183 18.4352C18.7617 18.6077 19.6209 18.2213 20.0514 17.4758L20.7677 16.235C21.1982 15.4894 21.1032 14.5521 20.5321 13.908L19.66 12.9246C19.5675 12.8202 19.5236 12.6819 19.5335 12.5428C19.5462 12.3635 19.5527 12.1826 19.5527 12.0001C19.5527 11.8176 19.5462 11.6366 19.5335 11.4574C19.5236 11.3182 19.5674 11.18 19.66 11.0756L20.5321 10.0922C21.1032 9.44806 21.1982 8.51074 20.7677 7.76521L20.0514 6.52441C19.6209 5.77888 18.7617 5.39243 17.9183 5.56501L16.6312 5.82842C16.4941 5.85646 16.3521 5.8251 16.2364 5.74656C15.9377 5.5438 15.6237 5.36189 15.2965 5.20291C15.1709 5.14189 15.073 5.0347 15.0288 4.90223L14.6138 3.6571C14.3415 2.84041 13.5773 2.28955 12.7164 2.28955H11.2836Z"
      stroke="white"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12 15.0001C13.6569 15.0001 15 13.6569 15 12.0001C15 10.3432 13.6569 9.00009 12 9.00009C10.3432 9.00009 9.00001 10.3432 9.00001 12.0001C9.00001 13.6569 10.3432 15.0001 12 15.0001Z"
      stroke="white"
    />
  </svg>
);

export const Sidebar = () => (
  <svg
    width="30"
    height="30"
    viewBox="0 0 30 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M23.75 15H13.75" stroke="white" strokeLinecap="round" />
    <path d="M23.75 8.75H6.25" stroke="white" strokeLinecap="round" />
    <path d="M23.75 21.25H8.75" stroke="white" strokeLinecap="round" />
  </svg>
);
