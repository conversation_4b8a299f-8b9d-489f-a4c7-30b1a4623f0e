import { SxProps, Theme } from "@mui/material";
import { memo, ReactNode } from "react";
import { ControlledTextField } from "./controlled-text-field";
import useDisableMouseWheel from "../features/create-requests/utils/useDisableMouseWheel";
import { Icon } from "../features/create-requests/assets";

type TextFieldWrapperProps = {
  name: string;
  locked?: boolean;
  multiline?: boolean;
  type?: "number" | "string";
  error?: string;
  helperWrapper?: ReactNode;
  onBlur?: () => void;
  onChange?: (value: number) => void;
  additionalStyles?: SxProps<Theme> | undefined;
};

export const TextFieldWrapper = memo(
  ({
    name,
    locked = false,
    multiline,
    type,
    helperWrapper,
    error,
    onBlur,
    onChange,
    additionalStyles,
  }: TextFieldWrapperProps) => {
    const inputRef = useDisableMouseWheel();
    return (
      <ControlledTextField
        ref={inputRef}
        name={name}
        multiline={multiline}
        fullWidth
        onBlur={onBlur}
        margin="dense"
        id={name}
        type={type}
        error={!!error}
        onBaseChange={onChange}
        helperText={helperWrapper ?? error}
        size="small"
        sx={{
          "& .MuiInputBase-input": { textAlign: "center" },
          ...additionalStyles,
        }}
        slotProps={{
          input: {
            endAdornment: locked ? <Icon.Lock /> : undefined,
            readOnly: locked,
          },
        }}
      />
    );
  }
);
