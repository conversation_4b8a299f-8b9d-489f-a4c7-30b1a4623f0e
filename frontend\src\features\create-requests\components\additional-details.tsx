import { ControlledCustomSelect, TextFieldWithLabel } from "../../../components";
import styles from "./additional-details.module.css";

type Props = {
  additionalServices: string[];
  dangerousGoods: string[];
};

export const AdditionalDetails = ({
  additionalServices,
  dangerousGoods,
}: Props) => {
  return (
    <div className={styles.additionalDetailsContainer}>
      <h3>Additional Details</h3>

      <div className={styles.inputsContainer}>
        <TextFieldWithLabel
          label="Description of goods"
          formName="additional_details.description_of_goods"
          multiline={true}
          additionalStyling={{
            "& .MuiInputBase-input": { textAlign: "start" },
          }}
        />
        <TextFieldWithLabel
          label="HS codes"
          formName="additional_details.hs_codes"
        />
        <TextFieldWithLabel
          label="Cost of goods"
          formName="additional_details.costs_of_goods"
          type="number"
        />

        <ControlledCustomSelect
          options={additionalServices}
          withLabel
          label="Additional services"
          name="additional_details.selected_services"
          multiple
          placeholder="Choose services"
        />

        <ControlledCustomSelect
          options={dangerousGoods}
          withLabel
          label="Dangerous Goods"
          name="additional_details.dangerous_goods"
          multiple
          placeholder="Choose goods"
        />
      </div>
    </div>
  );
};
