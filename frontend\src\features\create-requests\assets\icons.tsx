import { SvgIcon } from "@mui/material";

export const Close = () => (
  <svg
    width="22"
    height="23"
    viewBox="0 0 22 23"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15.7078 16.6354L5.80835 6.53931M15.7078 6.53931L5.80835 16.6354"
      stroke="#1B1D1F"
      strokeLinecap="round"
    />
  </svg>
);

export const Copy = () => (
  <SvgIcon>
    <svg
      width="26"
      height="24"
      viewBox="0 0 26 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="6.40869"
        y="5"
        width="12.8172"
        height="14"
        rx="0.5"
        stroke="#1B1D1F"
        strokeLinejoin="round"
      />
      <path
        d="M4.27246 17C4.27246 17 4.27246 6.09961 4.27246 3.49383C4.27246 3.21768 4.49632 3 4.77246 3H17.0897"
        stroke="#1B1D1F"
        strokeLinecap="round"
      />
    </svg>
  </SvgIcon>
);

export const Lock = () => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="3.75"
      y="8.25"
      width="10.5"
      height="7.5"
      rx="0.5"
      stroke="#C6C5CA"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.25 6C5.25 3.92893 6.92893 2.25 9 2.25V2.25C11.0711 2.25 12.75 3.92893 12.75 6V8.25H5.25V6Z"
      stroke="#C6C5CA"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Mail = () => (
  <svg
    width="26"
    height="24"
    viewBox="0 0 26 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="2.17944"
      y="5"
      width="21.362"
      height="14"
      rx="0.5"
      stroke="#1B1D1F"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.17944 5L12.5383 13.7285C12.7244 13.8854 12.9965 13.8854 13.1826 13.7285L23.5415 5"
      stroke="#1B1D1F"
    />
    <path d="M2.17944 19L9.65615 11" stroke="#1B1D1F" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M23.5414 19L16.0647 11L23.5414 19Z"
      stroke="#1B1D1F"
    />
  </svg>
);

export const OpenSidebar = () => (
  <svg
    width="30"
    height="30"
    viewBox="0 0 30 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M23.75 15H13.75" stroke="#1B1D1F" strokeLinecap="round" />
    <path d="M23.75 8.75H6.25" stroke="#1B1D1F" strokeLinecap="round" />
    <path d="M23.75 21.25H8.75" stroke="#1B1D1F" strokeLinecap="round" />
  </svg>
);

export const Paste = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.6667 2H12.8889C13.1836 2 13.4662 2.14048 13.6746 2.39052C13.8829 2.64057 14 2.97971 14 3.33333V12.6667C14 13.0203 13.8829 13.3594 13.6746 13.6095C13.4662 13.8595 13.1836 14 12.8889 14H10.6667M6.66667 11.3333L10 8M10 8L6.66667 4.66667M10 8H2"
      stroke="#70B57D"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Plus = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M20 12H4M12 4V20" stroke="white" strokeLinecap="round" />
  </svg>
);

export const Send = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_390_7314)">
      <path
        d="M21.9999 12.0001H6.04993"
        stroke="white"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21.9999 12L3.57544 20.6522C3.16236 20.8462 2.71879 20.4475 2.88337 20.0301L5.98011 12.1773C6.0251 12.0632 6.0251 11.9369 5.98011 11.8228L2.88337 3.96996C2.71879 3.55262 3.16236 3.15391 3.57544 3.34789L21.9999 12Z"
        stroke="white"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_390_7314">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const Trash = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 11L10 18M14 11V18M19 8L18 20.25C18 20.7141 17.7893 21.1592 17.4142 21.4874C17.0391 21.8156 16.5304 22 16 22H8C7.46957 22 6.96086 21.8156 6.58579 21.4874C6.21071 21.1592 6 20.7141 6 20.25L5 8H19ZM9 4V3C9 2.44772 9.44772 2 10 2H14C14.5523 2 15 2.44772 15 3V4C15 4.55228 14.5523 5 14 5H10C9.44772 5 9 4.55228 9 4ZM4 7.5V5.5C4 5.22386 4.22386 5 4.5 5H19.5C19.7761 5 20 5.22386 20 5.5V7.5C20 7.77614 19.7761 8 19.5 8H4.5C4.22386 8 4 7.77614 4 7.5Z"
      stroke="#EB4E3D"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

