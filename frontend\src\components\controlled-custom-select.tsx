import { SxProps, Theme } from "@mui/material";
import { Controller } from "react-hook-form";
import { CustomSelect } from "./custom-select";
import { CustomSelectWithLabel } from "./custom-select-with-label";

type Props = {
  label: string;
  options: string[];
  styling?: SxProps<Theme> | undefined;
  name: string;
  handleChange?: (selectedValue?: string) => void;
  withLabel?: boolean;
  maxWidth?: number;
  multiple?: boolean;
  placeholder?: string;
};

export const ControlledCustomSelect = ({
  label,
  options,
  styling,
  name,
  handleChange,
  withLabel,
  maxWidth,
  multiple,
  placeholder,
}: Props) => {
  return (
    <Controller
      name={name}
      render={({ field, fieldState }) =>
        withLabel ? (
          <CustomSelectWithLabel
            {...field}
            fieldState={fieldState}
            placeholder={placeholder}
            multiply={multiple}
            label={label}
            options={options}
            additionalStyling={styling}
            maxWidth={maxWidth}
            onChange={(e) => {
              field.onChange(e);
              handleChange?.(e.target.value);
            }}
          />
        ) : (
          <CustomSelect
            label={label}
            options={options}
            {...field}
            {...styling}
            onChange={(e) => {
              field.onChange(e);
              handleChange?.(e.target.value);
            }}
          />
        )
      }
    />
  );
};
