import type { Logger } from "pino";
import type { Request, Response, NextFunction } from "express";
import type { RateLimitRequestHandler } from "express-rate-limit";
import type { Config } from "../config";
import type { ExtractService } from "../extract";
import type { AuthService } from "../auth";
import type { UserService } from "../user";

import { z } from "zod";
import cors from "cors";
import helmet from "helmet";
import express from "express";
import session from "express-session";
import FileStore from "session-file-store";
import cookieParser from "cookie-parser";
import errorHandler from "strong-error-handler";
import { rateLimit } from "express-rate-limit";
import audit from "express-requests-logger";
import * as Api from "../shared/api";
import { bodyValidator } from "../middleware";
import { Injectable, trycatchAsync } from "../utils";
import { createSignalErrorHandler } from "../error/http";
import { AlreadyExistsSignalError, InternalServerErrorSignalError, NotFoundSignalError } from "../error/http";
import { SignalError } from "../error/signal";

// Extend Express Request type to include session user
declare module "express-session" {
    interface SessionData {
        userId: string;
    }
}

const S_PER_DAY = 24 * 60 * 60;
const MS_PER_DAY = S_PER_DAY * 1000;

/**
 * @param window - minutes, default 60
 */
function createRateLimit(limit: number, window = 60): RateLimitRequestHandler {
    return rateLimit({
        windowMs: window * 60 * 1000,
        limit,
        standardHeaders: "draft-8",
        legacyHeaders: false,
    });
}

export class Server extends Injectable {
    private readonly isDev!: boolean;

    private readonly logger!: Logger;
    private readonly config!: Config;
    private readonly extractService!: ExtractService;
    private readonly authService!: AuthService;
    private readonly userService!: UserService;

    run() {
        const app = express();

        app.use(
            errorHandler({
                debug: this.isDev,
                log: true,
            }),
        );

        // Basic middleware
        app.use(
            cors(),
            helmet(),
            cookieParser(),
            express.json(),
            express.urlencoded({ extended: true }),
            audit({
                logger: this.logger,
            }),
        );

        const sessionExpireTimeDays = this.config.getWithValidation(
            "SESSION_EXPIRE_TIME_DAYS",
            z.coerce.number().int().positive(),
        );

        // Configure session middleware
        const FileStoreSession = FileStore(session);
        app.use(session({
            secret: this.config.get<string>("SESSION_SECRET"),
            resave: false,
            saveUninitialized: false,
            name: "session",
            cookie: {
                secure: false, // !this.isDev, // Use secure cookies in production
                httpOnly: true,
                maxAge: sessionExpireTimeDays * MS_PER_DAY,
            },
            store: new FileStoreSession({
                path: "./.sessions",
                ttl: sessionExpireTimeDays * S_PER_DAY,
            }),
        }));

        // Authentication middleware
        const requireAuth = (req: Request, res: Response, next: NextFunction) => {
            if (!req.session.userId) {
                res.sendStatus(401);

                return;
            }

            next();
        };

        app.post(
            "/auth/otp",
            createRateLimit(60),
            bodyValidator(Api.OtpRequest),
            async (req, res) => {
                const data = req.body as Api.OtpRequest;

                await this.authService.otp(data);

                res.sendStatus(200);
            },
        );

        app.post(
            "/auth/sign-up",
            createRateLimit(60),
            bodyValidator(Api.SignUpRequest),
            async (req, res) => {
                const data = req.body as Api.SignUpRequest;

                await trycatchAsync(
                    async () => {
                        const user = await this.authService.signUp(data);

                        // Regenerate session
                        req.session.regenerate((err) => {
                            if (err) {
                                this.logger.error("Session regeneration error:");
                                this.logger.error(err);

                                throw new InternalServerErrorSignalError("session regeneration");
                            }

                            // Store user ID in session
                            req.session.userId = user.id;

                            // Save session before responding
                            req.session.save((err) => {
                                if (err) {
                                    this.logger.error("Session save error:");
                                    this.logger.error(err);

                                    throw new InternalServerErrorSignalError("session save");
                                }

                                res.sendStatus(201);
                            });
                        });
                    },
                    [
                        [SignalError, (e) => { console.log("signal error", e); throw e }],
                    ],
                    (e) => {
                        console.log("catch-all", e);
                        if (e instanceof Error && e.message.includes("nique constraint")) {
                            throw new AlreadyExistsSignalError("user");
                        }

                        this.logger.error("Sign-up error:");
                        this.logger.error(e);

                        throw new InternalServerErrorSignalError("sign up");
                    },
                );
            },
        );

        // Authentication routes
        app.post(
            "/auth/sign-in",
            createRateLimit(60),
            bodyValidator(Api.SignInRequest),
            async (req, res) => {
                const data = req.body as Api.SignInRequest;

                await trycatchAsync(
                    async () => {
                        const user = await this.authService.signIn(data);

                        // Regenerate session to prevent session fixation
                        req.session.regenerate((err) => {
                            if (err) {
                                this.logger.error("Session regeneration error:");
                                this.logger.error(err);

                                throw new InternalServerErrorSignalError("session regeneration");
                            }

                            // Store user ID in session
                            req.session.userId = user.id;

                            // Save session before responding
                            req.session.save((err) => {
                                if (err) {
                                    this.logger.error("Session save error:");
                                    this.logger.error(err);

                                    throw new InternalServerErrorSignalError("session save");
                                }

                                res.sendStatus(200);
                            });
                        });
                    },
                    [
                        [SignalError, (e) => { throw e }],
                    ],
                    (e) => {
                        this.logger.error("Authentication error:");
                        this.logger.error(e);

                        throw new InternalServerErrorSignalError("authentication", e);
                    },
                );
            },
        );

        app.post(
            "/auth/sign-out",
            createRateLimit(60),
            (req, res) => {
                // Destroy the session
                req.session.destroy((err) => {
                    if (err) {
                        this.logger.error("Session destruction error:");
                        this.logger.error(err);

                        throw new InternalServerErrorSignalError("session destruction");
                    }

                    // Clear the cookie
                    res.clearCookie("session");
                    res.sendStatus(200);
                });
            });

        app.post(
            "/auth/reset-password",
            createRateLimit(60),
            bodyValidator(Api.ResetPasswordRequest),
            async (req, res) => {
                const data = req.body as Api.ResetPasswordRequest;

                await this.authService.resetPassword(data);

                res.sendStatus(200);
            },
        );

        app.get("/auth/me", requireAuth, async (req, res) => {
            const user = await this.userService.getById(req.session.userId!);

            if (!user) {
                throw new NotFoundSignalError("user");
            }

            res.json({
                id: user.id,
                email: user.email,
            });
        });

        // Extract route
        app.post(
            "/extract",
            bodyValidator(Api.ExtractRequest),
            async (req, res) => {
                const { text } = req.body as z.infer<typeof Api.ExtractRequest>;

                const result = await this.extractService.extractV2({
                    text,
                });

                const rawResponse = {
                    service_type: result.metadata.serviceType,
                    incoterms: result.metadata.incoterms?.join(", "),
                    origin: result.metadata.origin.country,
                    destination_country: result.metadata.destination.country,

                    pickup: {
                        city: result.metadata.origin.city,
                        zip_code: result.metadata.origin.zipCode,
                        address: result.metadata.origin.address,
                        is_needed: false,
                    },

                    delivery: {
                        city: result.metadata.destination.city,
                        zip_code: result.metadata.destination.zipCode,
                        address: result.metadata.destination.address,
                        is_needed: false,
                    },

                    summary: {
                        piece: result.digits.totalUnits,
                        weight: result.digits.totalWeight,
                        volume: result.digits.totalVolume,
                        density: null,
                        chargeable_weight: null,
                    },

                    details: result.digits.packages?.map((item) => ({
                        piece: item.units,
                        dimension: {
                            width: item.width ?? undefined,
                            height: item.height ?? undefined,
                            length: item.length ?? undefined,
                            volume: undefined,
                            weight: item.weight ?? undefined,
                            is_stackable: item.isStackable,
                        },
                    })),

                    additional_details: {
                        description_of_goods: result.services.descriptionOfGoods,
                        hs_codes: result.services.hsCodes?.[0] ?? null,
                        costs_of_goods: result.services.totalCostOfGoods,
                        services: null,
                        selected_services: result.services.additionalServices,
                    },
                } satisfies z.input<typeof Api.ExtractResponse>;

                const response = Api.ExtractResponse.safeParse(rawResponse);

                if (response.success) {
                    res.json(response.data);
                }
                else {
                    throw new InternalServerErrorSignalError("transforming response");
                }
            },
        );

        app.use(createSignalErrorHandler());

        const port = this.config.getWithValidation(
            "SERVER_PORT",
            z.coerce.number().int().positive(),
        );

        app.listen(port, () => {
            this.logger.info(`Server is running on port ${port}`);
        });
    }
}
