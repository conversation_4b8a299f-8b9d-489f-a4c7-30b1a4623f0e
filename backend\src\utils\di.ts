import { ConcurrentRun<PERSON> } from "./concurrent-runner";

export abstract class Injectable {
    /**
     * Injectable must override init()'s.
     */
    constructor() {}

    protected async init1(): Promise<void> {}
    protected async init2(): Promise<void> {}
    protected async init3(): Promise<void> {}
}

interface OpenInjectable {
    init1(): Promise<void>;
    init2(): Promise<void>;
    init3(): Promise<void>;
}

export type AsValue<T = unknown> = { asValue: T };
export type AsClass<T = Injectable> =
    T extends Injectable
        ? { asClass: { new (...args: unknown[]): T } }
        : never;

export type AnyAs<T = unknown> = AsClass<T> | AsValue<T>;

export type ContainerInitialData = Record<string, AsValue | AsClass<Injectable>>;

export type ContainerData<T extends ContainerInitialData> = Normalize<{
    [Key in keyof T]:
        T[Key] extends AsValue<infer V>
            ? V
            : T[Key] extends AsClass<infer C>
                ? C
                : never;
}>;

export class Container<T extends Record<string, unknown> = Record<string, unknown>> {
    protected readonly data: T = {} as T;
    protected readonly injectables: Record<string, OpenInjectable> = {};

    register(data: Partial<{ [Key in keyof T]: AnyAs<T[Key]> }>) {
        Object.entries(data).forEach(([key, asObject]) => {
            if ("asClass" in asObject) {
                this.data[key as keyof T] = new asObject.asClass();
                this.injectables[key] = this.data[key as keyof T] as OpenInjectable;
            }
            else {
                this.data[key as keyof T] = asObject.asValue;
            }
        });

        return this;
    }

    inject() {
        Object
            .values(this.injectables)
            .forEach((injectable) => Object.assign(injectable, this.data));

        return this;
    }

    async init(maxConcurrent = 1) {
        await ConcurrentRunner.run(
            Object
                .values(this.injectables)
                .map((injectable) => () => injectable.init1()),
            maxConcurrent,
        );

        await ConcurrentRunner.run(
            Object
                .values(this.injectables)
                .map((injectable) => () => injectable.init2()),
            maxConcurrent,
        );

        await ConcurrentRunner.run(
            Object
                .values(this.injectables)
                .map((injectable) => () => injectable.init3()),
            maxConcurrent,
        );

        return this;
    }

    resolve<TKey extends keyof T> (key: TKey): T[TKey] {
        if (key in this.data) {
            return this.data[key];
        }

        throw new Error(`Key ${String(key)} not found in container.`);
    }
}
