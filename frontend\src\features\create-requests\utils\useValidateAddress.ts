import { useFormContext } from "react-hook-form";
import { ShippingInfo, ShippingType } from "../types/shippingTypes";
import { REQUIRED } from "./errorMessages";
import { isCityValid } from "./validationFunctions";

export const useValidateAddress = () => {
  const { getValues, setError, clearErrors } = useFormContext<ShippingInfo>();

  const validateAddress = (type: ShippingType) => {
    const formData = getValues();
 
    if (isCityValid(formData, type)) {
      clearErrors(`${type}.city`);
    } else {
      setError(`${type}.city`, {
        type: "required",
        message: REQUIRED,
      });
    }
  };

  return validateAddress;
};
