import type { Config } from "../config";
import type { LlmService } from ".";

import { z } from "zod";
import OpenAI from "openai";
import { Injectable } from "../utils";
import { zodResponseFormat } from "openai/helpers/zod.mjs";

export class OpenAiService extends Injectable implements LlmService {
    private readonly config!: Config;

    private openai!: OpenAI;

    override async init1() {
        const apiKey = this.config.getWithValidation(
            "OPENAI_API_KEY",
            z.string().nonempty(),
        );

        this.openai = new OpenAI({ apiKey });
    }

    async chatCompletion<T>(data: {
        model?: string;
        messages: OpenAI.ChatCompletionMessageParam[];
        responseFormat: {
            schema: z.ZodSchema<T>;
            name: string;
        }
    }) {
        const dto = {
            model: data.model ?? "gpt-4o-2024-08-06", // gpt-4o-mini?
            messages: data.messages,
            response_format: zodResponseFormat(
                data.responseFormat.schema,
                data.responseFormat.name,
            ),
        };

        const chatCompletion = await this.openai.beta.chat.completions.parse(dto);

        if (!chatCompletion.choices[0]) {
            console.log();
            throw new Error("No choices returned from OpenAI");
        }

        return chatCompletion.choices[0]!.message.parsed!;
    }
}
