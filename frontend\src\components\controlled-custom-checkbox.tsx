import { useController } from "react-hook-form";
import { CustomCheckbox } from "./custom-checkbox";

type Props = {
  formName: string;
  label?: string;
  handleChange?: () => void;
};

export const ControlledCustomCheckbox = ({ label, formName, handleChange }: Props) => {
  const {
    field: { onChange, value },
  } = useController({
    name: formName,
  });

  const handleCheckboxChange = (data: unknown) => {
    onChange(data);
    handleChange?.();
  }

  return (
    <CustomCheckbox
      formName={formName}
      label={label}
      onChange={handleCheckboxChange}
      checked={Boolean(value)}
    />
  );
};
