import React from 'react';
import { Modal as MuiModa<PERSON>, Box, IconButton, Typography, Backdrop } from '@mui/material';
import { styled } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';

interface ModalProps {
  open: boolean;
  onClose: () => void;
  message: string | null;
}

const StyledBackdrop = styled(Backdrop)(({ theme }) => ({
  backgroundColor: 'rgba(0, 0, 0, 0.6)',
  backdropFilter: 'blur(4px)',
  transition: theme.transitions.create(['backdrop-filter', 'background-color'], {
    duration: theme.transitions.duration.standard,
  }),
}));

const ModalContainer = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  backgroundColor: '#ffffff',
  borderRadius: '12px',
  boxShadow: '0 24px 48px rgba(0, 0, 0, 0.2), 0 8px 16px rgba(0, 0, 0, 0.1)',
  padding: '32px',
  minWidth: '320px',
  maxWidth: '600px',
  width: '90vw',
  maxHeight: '80vh',
  overflow: 'auto',
  outline: 'none',
  transition: theme.transitions.create(['transform', 'opacity'], {
    duration: theme.transitions.duration.standard,
  }),
  '&:focus': {
    outline: 'none',
  },
}));

const CloseButton = styled(IconButton)(({ theme }) => ({
  position: 'absolute',
  top: '12px',
  right: '12px',
  color: '#666666',
  backgroundColor: 'transparent',
  border: 'none',
  borderRadius: '50%',
  width: '32px',
  height: '32px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  transition: theme.transitions.create(['background-color', 'color'], {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    backgroundColor: 'rgba(0, 0, 0, 0.04)',
    color: '#333333',
  },
  '&:active': {
    backgroundColor: 'rgba(0, 0, 0, 0.08)',
  },
}));

const MessageText = styled(Typography)(() => ({
  fontFamily: "'YS Text', sans-serif",
  fontSize: '16px',
  lineHeight: '1.5',
  color: '#333333',
  marginTop: '8px',
  wordBreak: 'break-word',
  whiteSpace: 'pre-wrap',
}));

export const Modal: React.FC<ModalProps> = ({ open, onClose, message }) => {
  const handleBackdropClick = (event: React.MouseEvent) => {
    // Only close if clicking directly on the backdrop, not on the modal content
    if (event.target === event.currentTarget) {
      onClose();
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Escape') {
      onClose();
    }
  };

  return (
    <MuiModal
      open={open}
      onClose={onClose}
      closeAfterTransition
      slots={{ backdrop: StyledBackdrop }}
      slotProps={{
        backdrop: {
          timeout: 300,
        },
      }}
    >
      <ModalContainer
        onClick={handleBackdropClick}
        onKeyDown={handleKeyDown}
        tabIndex={-1}
      >
        <CloseButton
          onClick={onClose}
          aria-label="Close modal"
          size="small"
        >
          <CloseIcon fontSize="small" />
        </CloseButton>

        <MessageText variant="body1">
          {message}
        </MessageText>
      </ModalContainer>
    </MuiModal>
  );
};