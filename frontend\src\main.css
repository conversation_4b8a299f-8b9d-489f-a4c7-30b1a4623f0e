@font-face {
  font-family: 'YS Text';
  src: url('/assets/fonts/YS\ Text-Regular.ttf') format('truetype');
  font-size: 400;
}

@font-face {
  font-family: 'YS Text';
  src: url('/assets/fonts/YS\ Text-Bold.ttf') format('truetype');
  font-weight: 700;
}

@font-face {
  font-family: 'YS Text';
  src: url('/assets/fonts/YS\ Text-Medium.ttf') format('truetype');
  font-weight: 500;
}

@font-face {
  font-family: 'YS Text';
  src: url('/assets/fonts/YS\ Text-Light.ttf') format('truetype');
  font-weight: 300;
}

html {
  scroll-behavior: smooth;
  overflow-y: scroll; /* Always show vertical scrollbar to prevent layout shifts */
  scrollbar-width: thin; /* For Firefox - make scrollbar thinner */
}

:root {
  --drawer-width: 190px;
}

button {
    background: none;
    border: none;
    margin: 0;
    padding: 0;
    cursor: pointer;
    box-sizing: border-box;
    font-family: 'YS Text',  sans-serif;
    line-height: 16px;
    letter-spacing: 0.5px;
}


textarea, div, p {
    scrollbar-width: none; /* For Firefox */
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    line-height: 16px;
  }

textarea {
  font-family: 'YS Text',  sans-serif;
letter-spacing: 0.5px;
  }

textarea::-webkit-scrollbar {
  display: none; /* For Chrome, Safari, and Edge */
}

/* Customize scrollbar for WebKit browsers */
::-webkit-scrollbar {
  width: 8px; /* Width of the scrollbar */
}

::-webkit-scrollbar-track {
  background: transparent; /* Track background */
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2); /* Scrollbar color */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3); /* Scrollbar color on hover */
}

#root, html, body {
  width: 100%;
  padding: 0;
  margin: 0;
  font-family: 'YS Text',  sans-serif;
  letter-spacing: 0.5px;
  line-height: 16px;
}

#root {
  display: flex;
}

h3 {
  font-size: 16px;
}

#root > * {
  letter-spacing: 0.5px;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  appearance: textfield;
  -moz-appearance: textfield;
}