import { FormControl } from "@mui/material";
import { useFormContext } from "react-hook-form";
import { ControlledTextField, ControlledCustomCheckbox } from "../../../components";
import { ShippingType } from "../types/shippingTypes";
import styles from "./address-info.module.css";

type Props = {
  label: string;
  checkboxLabel: string;
  type: ShippingType;
};

export const AddressInfo = ({ label, checkboxLabel, type }: Props) => {
  const { trigger } = useFormContext();
  return (
    <div className={styles.addressInfoContainer}>
      <div className={styles.labelContainer}>
        <div>{label}</div>

        <ControlledCustomCheckbox
          label={checkboxLabel}
          formName={`${type}.is_needed`}
          handleChange={() => trigger([`${type}.city`])}
        />
      </div>
      <div className={styles.firstLineContainer}>
        <ControlledTextField
          name={`${type}.city`}
          id={`${type}.city`}
          label="City"
          size="small"
          onChange={() => trigger([`${type}.city`])}
          sx={{
            maxWidth: 188,
            flexShrink: 1,
            flexGrow: 2,
          }}
        />
        <FormControl>
          <ControlledTextField
            id={`${type}.zip_code`}
            name={`${type}.zip_code`}
            label="ZIP code"
            size="small"
            sx={{ maxWidth: "100px", flexShrink: 2 }}
          />
        </FormControl>
      </div>
      <ControlledTextField
        name={`${type}.address`}
        fullWidth
        label="Address"
        size="small"
        sx={{ flexGrow: 1 }}
      />
    </div>
  );
};
