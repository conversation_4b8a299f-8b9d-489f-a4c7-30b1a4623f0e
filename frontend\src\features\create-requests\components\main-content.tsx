import { useCallback, useState } from "react";
import { CustomAlert } from "../../../components";
import { useSendPrompt } from "../data/useSendPrompt";
import styles from "./main-content.module.css";

import {
  Main,
  SendButton,
  TextInfoInput,
  ResultText,
  FormWrapper,
} from ".";

type Props = {
  sidebarOpen: boolean;
  formRef: React.RefObject<HTMLFormElement | null>;
  initalInputText: string;
  setInitalInputText: React.Dispatch<React.SetStateAction<string>>
};

export type TResultText = {
  title: string;
  mainContent: string;
};

export const MainContent = ({
  sidebarOpen,
  formRef,
  initalInputText,
  setInitalInputText,
}: Props) => {
  const { sendPrompt, loading, error, data } = useSendPrompt();
  const [resultText, setResultText] = useState({
    title: "",
    mainContent: "",
  });

  const handleResultMainTextChange = useCallback((text: TResultText) => {
    setResultText(text);
  }, []);

  const handleTextChange = (newText: string) => {
    setInitalInputText(newText);
  };

  const handleSendButtonClick = async () => {
    await sendPrompt(initalInputText);

    if (formRef.current) {
      formRef.current.reset();
    }
  };

  return (
    <>
      <CustomAlert message={error} severity="error" />
      <Main open={sidebarOpen}>
        <div className={styles.contentContainer}>
          <div className={styles.textContainer}>
            <div className={styles.label}>
              Paste here the text of request for cargo transportation
            </div>

            <SendButton
              loading={loading}
              disabled={!initalInputText}
              handleSendButtonClick={handleSendButtonClick}
            />
            <div className={styles.textInputsContainer}>
              <TextInfoInput
                text={initalInputText}
                handleTextChange={handleTextChange}
              />
              <ResultText {...resultText} />
            </div>
          </div>
          <FormWrapper
            data={data}
            ref={formRef}
            setResultText={handleResultMainTextChange}
          />
        </div>
      </Main>
    </>
  );
};
