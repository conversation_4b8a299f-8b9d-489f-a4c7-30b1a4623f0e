import type { PrismaClient } from "@prisma/client";

import { Injectable } from "../utils";

export class UserService extends Injectable {
    private readonly prisma!: PrismaClient;

    async create(dto: {
        email: string,
        hash: string,
    }) {
        const user = await this.prisma.user.create({
            data: {
                email: dto.email,
                hash: dto.hash,
            },
        });

        return user;
    }

    async getById(id: string) {
        const user = await this.prisma.user.findUnique({
            where: { id },
        });

        return user;
    }

    async getByEmail(email: string) {
        const user = await this.prisma.user.findUnique({
            where: { email },
        });

        return user;
    }

    async updateHash(dto: {
        email: string;
        hash: string;
    }) {
        await this.prisma.user.update({
            where: {
                email: dto.email,
            },
            data: {
                hash: dto.hash,
            },
        });
    }
}
