.header {
    display: flex;
    margin: 35px 16px 51px;
    justify-content: space-between;
}

.container {
    display: flex;
    justify-content: end;
    gap: 16px;
    flex-shrink: 1;
    width: 100%;
}


.base {
    width: 30px;
    height: 30px;
    visibility: visible;
    align-items: center;
  }

  /* When Sidebar is Open (AppBar Hidden) */
.open {
    width: 30px;
    height: 30px;
    margin-left: -200px;
    visibility: hidden;
}

  /* When Sidebar is Closed (AppBar Visible) */
.visible {
    width: 30px;
    height: 30px;
    margin-left: 0;
    visibility: visible;
    transition: margin-left 0.1s ease-in-out, width 0.5s ease-in-out, height 0.5s ease-in-out;
}