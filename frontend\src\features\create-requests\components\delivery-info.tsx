import { useFormContext } from "react-hook-form";
import { ControlledCustomSelect, ControlledTextField } from "../../../components";
import {
  DELIVERY_REQUIRED_INCOTERMS,
  PICKUP_REQUIRED_INCOTERMS,
} from "../utils/consts";
import { SelectOption } from "../utils/useGetSelectOptions";
import styles from "./delivery-info.module.css";

type Props = SelectOption;

export const DeliveryInfo = ({ incoterms, service }: Props) => {
  const { clearErrors, trigger, setValue } = useFormContext();

  const handleIncotermsChange = (selectedIncoterms?: string) => {
    if (!selectedIncoterms) {
      return;
    }
    clearErrors([
      "incoterms",
      "pickup.city",
      "pickup.address",
      "delivery.address",
      "delivery.city",
    ]);

    if (DELIVERY_REQUIRED_INCOTERMS.includes(selectedIncoterms)) {
      setValue("delivery.is_needed", true);
    }

    if (PICKUP_REQUIRED_INCOTERMS.includes(selectedIncoterms)) {
      setValue("pickup.is_needed", true);
    }

    trigger([
      "pickup.city",
      "delivery.city",
      "delivery.address",
      "pickup.address",
    ]);
  };

  return (
    <div className={styles.deliveryInfoContainer}>
      <div>
        <ControlledCustomSelect
          label="Service"
          options={service}
          name="service_type"
          handleChange={() => clearErrors("service_type")}
        />
        <ControlledCustomSelect
          label="Incoterms"
          options={incoterms}
          name="incoterms"
          handleChange={handleIncotermsChange}
        />
      </div>

      <div>
        <ControlledTextField
          label="Origin"
          id="origin"
          name="origin"
          size="small"
          sx={{
            flexGrow: 1,
          }}
        />

        <ControlledTextField
          label="Destination"
          id="destination_country"
          size="small"
          name="destination_country"
          sx={{
            flexGrow: 1,
          }}
        />
      </div>
    </div>
  );
};
