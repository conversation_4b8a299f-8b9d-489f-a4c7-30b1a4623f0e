import { useState } from "react";
import { ShippingInfo } from "../types/shippingTypes";
import { SERVER_URL } from "../utils/consts";
import { z } from "zod";
import { UseFormSetError } from "react-hook-form";
import { FormData } from "../types/shippingTypes";

export const useSaveRequest = ({
  handleFieldErrors,
}: {
  handleFieldErrors:  UseFormSetError<FormData>
}) => {
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const saveRequest = async (data: ShippingInfo) => {
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch(`${SERVER_URL}/save`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (Array.isArray(errorData.details)) {
          errorData.details.forEach((error: { code: string; path: string[]; message: string; }) => {
            const path = error.path.join('.') as keyof FormData

            if (error.code === z.ZodIssueCode.invalid_type) {
              handleFieldErrors(path, {
                type: "manual",
                message: "Invalid data type",
              });
            } else {
              if (error.path[0]) {
                handleFieldErrors(path, {
                  type: "manual",
                  message: error.message || "Invalid data",
                });
              }
            }
          });
        } else {
          setError(errorData.message || "Failed to save request");
        }
        return;
      }

      setSuccess("Successfully saved");
    } catch {
      setError("An unexpected error occurred. Please try again");
    }
  };

  return { saveRequest, error, success };
};
