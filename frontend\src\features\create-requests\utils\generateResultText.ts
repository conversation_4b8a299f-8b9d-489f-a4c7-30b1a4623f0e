import { roundUpToTwoDecimals } from "../../../utils/roundUpToTwoDecimals";
import { ShippingInfoWithEmptyFields, Address } from "../types/shippingTypes";
import { getTotalPackageInfo } from "./calculateTotalPackageDetails";

const isObjectEmpty = <T extends object>(object?: T) => {
  return (
    object &&
    Object.values(object ?? {}).filter(Boolean).length === 0 &&
    object.constructor === Object
  );
};

const formatAddress = ({
  type,
  address,
}: {
  type: "Pickup" | "Delivery";
  address?: Address;
}) => {
  if (address?.is_needed) {
    return `${type} address: ${
      address?.zip_code ? address.zip_code + ", " : ""
    }${address?.address ? address.address + ", " : ""}${
      address?.city ? address.city : ""
    }`;
  }
};

const generateTitle = (formValues: ShippingInfoWithEmptyFields) => {
  const isIteraryEmpty = isObjectEmpty({
    incoterms: formValues.incoterms,
    origin: formValues.origin,
    destination: formValues.destination_country,
  });

  const itenrary = isIteraryEmpty
    ? ""
    : `${formValues.incoterms ? formValues.incoterms + " " : ""}${
        formValues.origin
      } – ${formValues.destination_country}`;

  const weight = formValues?.summary?.weight
    ? `${roundUpToTwoDecimals(formValues?.summary?.weight)} kgs`
    : "";

  const totalVolume = formValues?.summary?.volume
    ? `${roundUpToTwoDecimals(formValues?.summary?.volume)} cbm`
    : "";

  return [itenrary, weight, totalVolume].filter(Boolean).join(", ");
};

export const generateResultText = (formValues: ShippingInfoWithEmptyFields) => {
  const requestedService = formValues?.service_type
    ? `Requested service: ${formValues?.service_type}`
    : "";

  const isIteraryEmpty = isObjectEmpty({
    incoterms: formValues.incoterms,
    origin: formValues.origin,
    destination: formValues.destination_country,
  });

  const itenrary = isIteraryEmpty
    ? ""
    : `Itinerary: ${formValues.incoterms ? formValues.incoterms + " " : ""}${
        formValues.origin
      } – ${formValues.destination_country}`;

  const pickupAddress = formatAddress({
    type: "Pickup",
    address: formValues.pickup,
  });
  const deliveryAddress = formatAddress({
    type: "Delivery",
    address: formValues.delivery,
  });

  const hsCode = formValues.additional_details?.hs_codes
    ? `HS code: ${formValues.additional_details.hs_codes}`
    : "";

  const descriptionOfGoods = formValues.additional_details?.description_of_goods
    ? `Description of goods: ${formValues.additional_details.description_of_goods}`
    : "";

  const otherDetails = [
    formValues.additional_details?.selected_services?.length
      ? `Additional services: ${formValues.additional_details.selected_services.join(
          ", "
        )}`
      : "",
    formValues.additional_details?.dangerous_goods?.length
      ? `Dangerous goods: ${formValues.additional_details.dangerous_goods.join(
          ", "
        )}`
      : "",
  ]
    .filter(Boolean)
    .join("\n");

  const route = [requestedService, itenrary, pickupAddress, deliveryAddress]
    .filter(Boolean)
    .join("\n");

  const goodsInfo = getTotalPackageInfo(formValues);

  const goodsInfoLabel = goodsInfo.length
    ? `Package details:\n ${goodsInfo}`
    : "";

  const details = [descriptionOfGoods, hsCode, otherDetails]
    .filter(Boolean)
    .join("\n");

  const content = [route, goodsInfoLabel, details].filter(Boolean).join("\n\n");

  const title = generateTitle(formValues);

  return {
    title: title.length
      ? `Quotation
    ${title}`
      : "",
    mainContent: content.length
      ? `Dear colleagues, \nPlease provide your quotation on request: \n\n ${content}`
      : "",
  };
};
