{"env": {"es2022": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/eslint-recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"sourceType": "module"}, "plugins": ["@typescript-eslint", "@stylistic/eslint-plugin-js"], "ignorePatterns": ["/src/**/*.json", "/out"], "rules": {"no-multiple-empty-lines": ["error", {"max": 1, "maxBOF": 0, "maxEOF": 0}], "max-len": ["off", {"code": 120, "ignoreComments": true}], "indent": ["error", 4, {"SwitchCase": 1}], "linebreak-style": ["error", "unix"], "quotes": ["error", "double"], "semi": ["error", "always", {"omitLastInOneLineBlock": true}], "no-empty": ["error", {"allowEmptyCatch": true}], "comma-dangle": ["error", "always-multiline"], "eol-last": "error", "dot-notation": "off", "keyword-spacing": "error", "@typescript-eslint/no-unused-vars": "off", "no-trailing-spaces": "error", "newline-per-chained-call": ["error", {"ignoreChainWithDepth": 3}], "object-curly-spacing": ["error", "always"]}}