import {
  FormControl,
  MenuItem,
  Select,
  SelectChangeEvent,
  SxProps,
  Theme,
} from "@mui/material";
import { CustomizedInputLabel } from "./customized-input-label";

type Props = {
  label: string;
  options: string[];
  styling?: SxProps<Theme> | undefined;
  value?: string;
  onChange?: (e: SelectChangeEvent<string>) => void;
};

export const CustomSelect = ({
  label,
  options,
  styling,
  value,
  onChange,
}: Props) => {
  return (
    <FormControl
      sx={{
        flexBasis: "160px",
        flexGrow: 1,
        ...styling,
      }}
      size="small"
    >
      <CustomizedInputLabel>{label}</CustomizedInputLabel>
      <Select
        labelId={`${label}-label`}
        fullWidth
        label={label}
        value={value}
        onChange={onChange}
        sx={{
          height: "40px",
          borderRadius: "0",
          fontSize: "12px",
          " &.Mui-focused .MuiOutlinedInput-notchedOutline": {
            border: "2px solid green",
          },
        }}
        MenuProps={{
          disableScrollLock: true,
        }}
        slotProps={{
          input: {
            sx: {
              textTransform: "capitalize",
            },
          },
        }}
      >
        <MenuItem
          sx={{
            fontSize: "12px",
          }}
        >
          <em>None</em>{" "}
        </MenuItem>
        {options.map((item) => (
          <MenuItem
            key={item}
            value={item}
            sx={{
              textTransform: "capitalize",
              fontSize: "12px",
            }}
          >
            {item}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};
