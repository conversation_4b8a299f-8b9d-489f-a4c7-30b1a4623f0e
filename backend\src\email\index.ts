import type { Config } from "../config";

import nodemailer from "nodemailer";
import { Injectable } from "../utils";
import { z } from "zod";

export interface EmailDto {
    from?: string;
    to: string[];
    subject: string;
    text?: string;
    html?: string;
    cc?: string[];
    bcc?: string[];
    // attachments?: {
    //     filename: string;
    //     content: Buffer | string;
    //     contentType?: string;
    // }[];
}

export class EmailService extends Injectable {
    private config!: Config;

    private transporter!: nodemailer.Transporter;
    private defaultFrom!: string;

    override async init1() {
        const host = this.config.getWithValidation(
            "EMAIL_HOST",
            z.string().nonempty(),
        );
        const port = this.config.getWithValidation(
            "EMAIL_PORT",
            z.coerce.number().positive().int(),
        );
        const secure = this.config.getWithValidation(
            "EMAIL_SECURE",
            z.coerce.boolean(),
        );
        const authUser = this.config.getWithValidation(
            "EMAIL_USER",
            z.string().nonempty(),
        );
        const authPassword = this.config.getWithValidation(
            "EMAIL_PASSWORD",
            z.string().nonempty(),
        );

        // Initialize the nodemailer transporter using config
        this.transporter = nodemailer.createTransport({
            host,
            port,
            secure,
            auth: {
                user: authUser,
                pass: authPassword,
            },
        });

        this.defaultFrom = this.config.getWithValidation(
            "EMAIL_DEFAULT_FROM",
            z.string().email(),
        );
    }

    async send(dto: EmailDto) {
        try {
            const mailOptions = {
                from: dto.from ?? this.defaultFrom,
                to: dto.to,
                cc: dto.cc,
                bcc: dto.bcc,
                subject: dto.subject,
                text: dto.text,
                html: dto.html,
                // attachments: dto.attachments,
            };

            await this.transporter.sendMail(mailOptions);
        }
        catch (error) {
            console.error("Error sending email:", error);

            const message = error instanceof Error ? error.message : String(error);
            throw new Error(`Failed to send email: ${message}`);
        }
    }
}
